### План за Имплементация на Интерфейса

Този документ описва техническата имплементация, необходима за създаването на уебсайта, базирана на `plan/interface.md`. Планът обхваща структурите от данни (модели), логиката за обработка на заявки (изгледи) и управлението на съдържанието (админ панел).

---

### Структура на Проекта

Това е предложената структура на директориите и файловете, за да се организира кода чисто и логично.

```
imoti-claude4/
├── config/                  # Django конфигурация
│   ├── settings.py
│   └── urls.py
├── core/                    # Главно приложение
│   ├── migrations/
│   ├── templates/
│   │   ├── core/
│   │   │   ├── base.html              # Основен темплейт
│   │   │   ├── index.html             # Начална страница
│   │   │   ├── property_list.html     # Списък с имоти (основна страница)
│   │   │   ├── property_detail.html   # Детайлна страница на имот
│   │   │   ├── about.html             # За нас
│   │   │   ├── contact.html           # Контакти
│   │   │   ├── privacy_policy.html    # Политика за поверителност
│   │   │   └── terms_of_service.html  # Общи условия
│   │   └── partials/
│   │       └── property_list_results.html # HTML фрагмент за htmx
│   ├── __init__.py
│   ├── admin.py               # Конфигурация на админ панела
│   ├── apps.py
│   ├── models.py              # Модели на данни
│   ├── urls.py                # URL пътища за приложението
│   └── views.py               # Изгледи (логика)
├── static/
│   ├── css/
│   │   └── main.css
│   └── js/
│       └── main.js
├── manage.py
└── requirements.txt
```

---

### 1. Модели на Данни (`core/models.py`)

Ще бъдат създадени следните модели, за да се съхранява цялата необходима информация за имотите, екипа и съдържанието на сайта.

- **`City`**: Модел за градове.

  - `name`: `CharField` - Име на града.

- **`Location`**: За съхранение на локации (квартали).

  - `name`: `CharField` - Име на локацията (напр. "Лозенец").
  - `city`: `ForeignKey` към `City` - За да се знае към кой град принадлежи кварталът.

- **`PropertyType`**: За различните типове имоти.

  - `name`: `CharField` - Име на типа (напр. "Апартамент", "Къща").
  - `slug`: `SlugField` - За използване в URL-и.

- **`Feature`**: За особеностите на имотите.

  - `name`: `CharField` - Име на особеността (напр. "Гараж", "Асансьор").

- **`TeamMember`**: За членовете на екипа (брокери).

  - `name`: `CharField` - Име на служителя.
  - `photo`: `ImageField` - Снимка.
  - `phone`: `CharField` - Телефонен номер.
  - `email`: `EmailField` - Имейл адрес.
  - `title`: `CharField` - Длъжност (напр. "Консултант").

- **`Property`**: Основният модел за обявите за имоти.

  - `title`: `CharField` - Заглавие на обявата.
  - `slug`: `SlugField` - Уникален идентификатор за URL.
  - `property_type`: `ForeignKey` към `PropertyType`.
  - `location`: `ForeignKey` към `Location`.
  - `price`: `DecimalField` - Цена.
  - `area`: `IntegerField` - Площ в кв.м.
  - `bedrooms`: `PositiveIntegerField` - Брой спални.
  - `description`: `TextField` - Пълно описание.
  - `features`: `ManyToManyField` към `Feature`.
  - `assigned_broker`: `ForeignKey` към `TeamMember`.
  - `is_published`: `BooleanField` - Дали обявата е видима на сайта.
  - `is_featured`: `BooleanField` - За секцията "Препоръчани имоти".
  - `created_at`, `updated_at`: `DateTimeField` - За проследяване на промените.

- **`PropertyImage`**: За галерията със снимки към всеки имот.

  - `property`: `ForeignKey` към `Property`.
  - `image`: `ImageField` - Самият файл със снимката.
  - `order`: `PositiveIntegerField` - За подредба на снимките в галерията.

- **`Testimonial`**: За отзиви от клиенти.

  - `client_name`: `CharField` - Име на клиента.
  - `quote`: `TextField` - Текст на отзива.
  - `is_active`: `BooleanField` - Дали да се показва на началната страница.

- **`ContactInquiry`**: За съхранение на запитвания от формите за контакт.
  - `name`, `email`, `phone`, `message`: Полета за данните от формата.
  - `property`: `ForeignKey` към `Property` (nullable) - За да се знае за коя обява е запитването.
  - `created_at`: `DateTimeField` - Кога е получено запитването.
  - `is_handled`: `BooleanField` - За отбелязване в админ панела дали е обработено.

---

### 2. Изгледи и URL-и (`core/views.py` и `core/urls.py`)

Ще бъдат създадени следните изгледи, които да отговарят на страниците от картата на сайта и да обработват динамичните заявки.

- **`home_view`**:

  - **URL**: `/`
  - **Функционалност**: Рендерира началната страница, като подава на темплейта списък с препоръчани имоти (`is_featured=True`) и активни отзиви.

- **`property_list_view`**:

  - **URL**: `/properties/`
  - **Функционалност**:
    - При стандартна заявка: рендерира пълната страница с всички публикувани имоти и филтрите.
    - При **htmx** заявка: получава параметрите за филтриране (`GET` заявка), филтрира имотите и връща само HTML фрагмент със списъка с резултати, който се инжектира в страницата без презареждане.

- **`property_detail_view`**:

  - **URL**: `/properties/<slug>/`
  - **Функционалност**: Рендерира детайлната страница за конкретен имот, като зарежда цялата му информация, снимки, информация за брокера и форма за запитване.

- **`contact_inquiry_create_view`**:

  - **URL**: `/contact-inquiry/` (ще се достъпва с `POST` заявка)
  - **Функционалност**: Обработва `POST` заявки от формите за контакт (от детайлна страница на имот и от страница "Контакти"). Валидира данните, създава `ContactInquiry` обект и връща съобщение за успех (подходящо за htmx).

- **Статични изгледи (чрез `TemplateView`)**:
  - **URLs**: `/about/`, `/contact/`, `/privacy-policy/`, `/terms-of-service/`
  - **Функционалност**: Рендерират съответните статични HTML темплейти. За `/about/` ще се подава и списък с членовете на екипа.

---

### 3. Админ Панел (`core/admin.py`)

За да може съдържанието да се управлява лесно, админ панелът на Django ще бъде конфигуриран по следния начин:

- **`PropertyAdmin`**:

  - Ще използва `inlines` за лесно добавяне и управление на `PropertyImage` директно от страницата на имота.
  - `list_display`: `title`, `location`, `price`, `is_published`, `is_featured`.
  - `list_filter`: `location`, `property_type`, `is_published`, `is_featured`.
  - `search_fields`: `title`, `description`.
  - `prepopulated_fields`: `{'slug': ('title',)}` за автоматично генериране на URL.

- **`TeamMemberAdmin`**, **`LocationAdmin`**, **`PropertyTypeAdmin`**, **`FeatureAdmin`**, **`TestimonialAdmin`**:

  - Ще бъдат регистрирани със стандартни конфигурации за лесно управление (`list_display`, `search_fields`).

- **`ContactInquiryAdmin`**:
  - `list_display`: `name`, `property`, `created_at`, `is_handled`.
  - `list_filter`: `is_handled`.
  - Полетата ще бъдат `readonly`, тъй като този модел служи само за преглед на получени запитвания.

Този план осигурява солидна основа за разработка, която е напълно съобразена с изискванията на интерфейса и следва добрите практики на Django.

---

### 4. Проследяване на Напредъка

За да се проследява ефективно работата по проекта, се използва подробен списък със задачи (To-Do list), който се намира във файла `plan/todo.md`.

**Ключово правило:** Този списък трябва да се поддържа **постоянно актуален**. При всяка промяна в статуса на задача (започната, завършена), съответният ред във файла `plan/todo.md` трябва да бъде обновен.

Това гарантира, че във всеки един момент има ясна представа за текущия статус на проекта и какво остава да бъде направено.

---

### 5. Качество на кода и Тестване

**Основно правило:** Разработката трябва да се ръководи от тестове, за да се гарантира качество и стабилност.

1.  **След всяка завършена задача:** Преди да се маркира задача от `plan/todo.md` като завършена, е **задължително**:

    - Да се напишат нови тестове (`core/tests.py`), които адекватно покриват имплементираната функционалност.
    - Да се изпълни целият набор от тестове в проекта (`python manage.py test`).

2.  **Блокиране при провал:**
    - Ако някой тест се провали (независимо дали е нов или стар), работата по следваща задача **не трябва да започва**.
    - Приоритет номер едно става поправянето на проблема, докато всички тестове отново преминат успешно.

Този подход гарантира, че новите промени не нарушават съществуваща функционалност (предотвратява регресии) и че всяка нова част от кода работи според очакванията.

---

### 6. Многоезичност (Internationalization - i18n)

За да се отговори на изискването за поддръжка на няколко езика (български, английски, гръцки, руски), ще се приложи стратегия, която обхваща както статичното, така и динамичното съдържание.

#### 6.1. Настройка на проекта

1.  **Middleware**: `django.middleware.locale.LocaleMiddleware` ще бъде добавен в `config/settings.py`. Той трябва да се намира след `SessionMiddleware` и преди `CommonMiddleware`, за да може да определи езика на базата на данните от заявката.
2.  **Езици**: В `settings.py` ще се дефинира списъкът с поддържани езици:
    ```python
    LANGUAGES = [
        ("bg", "Bulgarian"),
        ("en", "English"),
        ("el", "Greek"),
        ("ru", "Russian"),
    ]
    LANGUAGE_CODE = "bg" # Език по подразбиране
    ```
3.  **Езикови файлове**: Ще се създаде директория `locale/` в корена на проекта, където ще се съхраняват `.po` файловете за превод.

_Забележка: В Django 5+ `USE_I18N` и `USE_L10N` са активни по подразбиране и не е нужно да се добавят в настройките._

#### 6.2. URL-и с езиков префикс

В главния `config/urls.py`, пътищата, които трябва да са многоезични, ще бъдат обвити в `i18n_patterns`. Това автоматично ще добави езиков префикс (`/en/`, `/bg/`) към URL адресите. Ще бъде настроено и пренасочване от коренния URL (`/`) към езиковата версия по подразбиране (`/bg/`).

#### 6.3. Превод на статично съдържание

- **Шаблони**: Всички текстови низове в HTML темплейтите (`.html`) ще бъдат обвити с `{% trans "Текст за превод" %}` или `{% blocktrans %}` за по-дълги пасажи.
- **Python код**: Текстове в `.py` файлове (напр. имена на полета във форми, съобщения за грешки) ще се маркират с `from django.utils.translation import gettext_lazy as _`.
- **Генериране на файлове**: Ще се използват командите `python manage.py makemessages -l <код_на_език>` и `python manage.py compilemessages` за създаване и компилиране на `.po` файловете.

#### 6.4. Превод на динамично съдържание (Модели)

За да се избегне ръчното добавяне на полета за всеки език (`title_bg`, `title_en` и т.н.), ще се използва библиотеката **`django-modeltranslation`**.

- **Инсталация**: Пакетът ще бъде добавен в `requirements.txt` и инсталиран.
- **Конфигурация**: Ще бъде добавен в `INSTALLED_APPS`.
- **Регистрация за превод**: Ще се създаде файл `core/translation.py`, в който ще се дефинират кои полета от кои модели подлежат на превод. Например:

  ```python
  # core/translation.py
  from modeltranslation.translator import register, TranslationOptions
  from .models import Property, Feature

  @register(Property)
  class PropertyTranslationOptions(TranslationOptions):
      fields = ('title', 'description', 'slug') # Slug вече ще се превежда

  @register(Feature)
  class FeatureTranslationOptions(TranslationOptions):
      fields = ('name',)
  ```

- **Админ панел**: Библиотеката автоматично ще модифицира админ панела, за да предостави удобен интерфейс с табове за въвеждане на преводите за всеки език.
- **Миграции**: След конфигурацията ще се създадат и приложат нови миграции, които добавят необходимите колони в базата данни.

#### 6.5. Автоматично генериране на Slug-ове

Проблемът с ръчното създаване и поддържане на уникални и многоезични URL адреси (slugs) ще бъде решен с помощта на библиотеката **`django-autoslug`**.

- **Инсталация**: Пакетът ще бъде добавен в `requirements.txt` и инсталиран.
- **Модел**: Стандартното `SlugField` в `Property` модела ще бъде заменено с `AutoSlugField`.
- **Конфигурация**: `AutoSlugField` ще бъде настроен да:
  - Генерира slug автоматично от полето `title` (и други, ако е нужно, напр. локация).
  - Гарантира уникалност, като добавя инкрементални номера (`-2`, `-3`) при нужда.
  - **Интеграция с modeltranslation**: Ще бъде конфигуриран да работи с `django-modeltranslation`, за да създава отделен, преведен slug за всеки език (`slug_bg`, `slug_en` и т.н.).
- **Миграция на данни**: Ще се създаде специална миграция, която да попълни автоматично slug-овете за всички съществуващи записи в базата данни.

#### 6.6. Локализация на формати

Ще се използва вградената в Django система за локализация на формати за дати, числа и валути.

- **Настройка**: В `settings.py` е дефиниран `FORMAT_MODULE_PATH = "core.formats"`.
- **Файлове за формати**: Ще се създадат и попълнят файловете `core/formats/bg/formats.py`, `core/formats/en/formats.py` и т.н., за да се осигури коректно представяне на данните спрямо локалните стандарти.

#### 6.7. SEO оптимизация

- **Hreflang тагове**: Ще се създаде къстъм темплейт таг, който генерира `<link rel="alternate" hreflang="...">` тагове за всяка страница. Този таг ще итерира през `LANGUAGES` от настройките и ще генерира алтернативен URL за всеки език.
- **Мета тагове**: Логиката за генериране на мета тагове ще бъде обновена, за да използва преведените полета от моделите (`title`, `description`) за съответния език.
