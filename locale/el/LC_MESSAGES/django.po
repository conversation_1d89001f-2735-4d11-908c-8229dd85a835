# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-23 12:30+0300\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: core/templates/core/property_list.html:7
msgid "Локация"
msgstr "Τοποθεσία"

#: core/templates/core/property_list.html:8
msgid "Тип имот"
msgstr "Τύπος Ακινήτου"

#: core/templates/core/property_list.html:9
msgid "Търси имоти"
msgstr "Αναζήτηση Ακινήτων"

#: venv/lib/python3.13/site-packages/click/_termui_impl.py:600
#, python-brace-format
msgid "{editor}: Editing failed"
msgstr ""

#: venv/lib/python3.13/site-packages/click/_termui_impl.py:604
#, python-brace-format
msgid "{editor}: Editing failed: {e}"
msgstr ""

#: venv/lib/python3.13/site-packages/click/core.py:1084
#: venv/lib/python3.13/site-packages/click/core.py:1121
#, python-brace-format
msgid "{text} {deprecated_message}"
msgstr ""

#: venv/lib/python3.13/site-packages/click/core.py:1140
msgid "Options"
msgstr ""

#: venv/lib/python3.13/site-packages/click/core.py:1202
#, python-brace-format
msgid "Got unexpected extra argument ({args})"
msgid_plural "Got unexpected extra arguments ({args})"
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.13/site-packages/click/core.py:1221
msgid "DeprecationWarning: The command {name!r} is deprecated.{extra_message}"
msgstr ""

#: venv/lib/python3.13/site-packages/click/core.py:1405
msgid "Aborted!"
msgstr ""

#: venv/lib/python3.13/site-packages/click/core.py:1779
msgid "Commands"
msgstr ""

#: venv/lib/python3.13/site-packages/click/core.py:1810
msgid "Missing command."
msgstr ""

#: venv/lib/python3.13/site-packages/click/core.py:1888
msgid "No such command {name!r}."
msgstr ""

#: venv/lib/python3.13/site-packages/click/core.py:2303
msgid "Value must be an iterable."
msgstr ""

#: venv/lib/python3.13/site-packages/click/core.py:2324
#, python-brace-format
msgid "Takes {nargs} values but 1 was given."
msgid_plural "Takes {nargs} values but {len} were given."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.13/site-packages/click/core.py:2404
msgid ""
"DeprecationWarning: The {param_type} {name!r} is deprecated.{extra_message}"
msgstr ""

#: venv/lib/python3.13/site-packages/click/core.py:2807
#, python-brace-format
msgid "env var: {var}"
msgstr ""

#: venv/lib/python3.13/site-packages/click/core.py:2810
#, python-brace-format
msgid "default: {default}"
msgstr ""

#: venv/lib/python3.13/site-packages/click/core.py:2870
msgid "(dynamic)"
msgstr ""

#: venv/lib/python3.13/site-packages/click/decorators.py:465
#, python-format
msgid "%(prog)s, version %(version)s"
msgstr ""

#: venv/lib/python3.13/site-packages/click/decorators.py:522
msgid "Show the version and exit."
msgstr ""

#: venv/lib/python3.13/site-packages/click/decorators.py:548
msgid "Show this message and exit."
msgstr ""

#: venv/lib/python3.13/site-packages/click/exceptions.py:50
#: venv/lib/python3.13/site-packages/click/exceptions.py:89
#, python-brace-format
msgid "Error: {message}"
msgstr ""

#: venv/lib/python3.13/site-packages/click/exceptions.py:81
#, python-brace-format
msgid "Try '{command} {option}' for help."
msgstr ""

#: venv/lib/python3.13/site-packages/click/exceptions.py:130
#, python-brace-format
msgid "Invalid value: {message}"
msgstr ""

#: venv/lib/python3.13/site-packages/click/exceptions.py:132
#, python-brace-format
msgid "Invalid value for {param_hint}: {message}"
msgstr ""

#: venv/lib/python3.13/site-packages/click/exceptions.py:190
msgid "Missing argument"
msgstr ""

#: venv/lib/python3.13/site-packages/click/exceptions.py:192
msgid "Missing option"
msgstr ""

#: venv/lib/python3.13/site-packages/click/exceptions.py:194
msgid "Missing parameter"
msgstr ""

#: venv/lib/python3.13/site-packages/click/exceptions.py:196
#, python-brace-format
msgid "Missing {param_type}"
msgstr ""

#: venv/lib/python3.13/site-packages/click/exceptions.py:203
#, python-brace-format
msgid "Missing parameter: {param_name}"
msgstr ""

#: venv/lib/python3.13/site-packages/click/exceptions.py:223
#, python-brace-format
msgid "No such option: {name}"
msgstr ""

#: venv/lib/python3.13/site-packages/click/exceptions.py:235
#, python-brace-format
msgid "Did you mean {possibility}?"
msgid_plural "(Possible options: {possibilities})"
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.13/site-packages/click/exceptions.py:282
msgid "unknown error"
msgstr ""

#: venv/lib/python3.13/site-packages/click/exceptions.py:289
msgid "Could not open file {filename!r}: {message}"
msgstr ""

#: venv/lib/python3.13/site-packages/click/formatting.py:156
msgid "Usage:"
msgstr ""

#: venv/lib/python3.13/site-packages/click/parser.py:200
msgid "Argument {name!r} takes {nargs} values."
msgstr ""

#: venv/lib/python3.13/site-packages/click/parser.py:383
msgid "Option {name!r} does not take a value."
msgstr ""

#: venv/lib/python3.13/site-packages/click/parser.py:444
msgid "Option {name!r} requires an argument."
msgid_plural "Option {name!r} requires {nargs} arguments."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.13/site-packages/click/shell_completion.py:326
msgid "Shell completion is not supported for Bash versions older than 4.4."
msgstr ""

#: venv/lib/python3.13/site-packages/click/shell_completion.py:333
msgid "Couldn't detect Bash version, shell completion is not supported."
msgstr ""

#: venv/lib/python3.13/site-packages/click/termui.py:162
msgid "Repeat for confirmation"
msgstr ""

#: venv/lib/python3.13/site-packages/click/termui.py:178
msgid "Error: The value you entered was invalid."
msgstr ""

#: venv/lib/python3.13/site-packages/click/termui.py:180
#, python-brace-format
msgid "Error: {e.message}"
msgstr ""

#: venv/lib/python3.13/site-packages/click/termui.py:191
msgid "Error: The two entered values do not match."
msgstr ""

#: venv/lib/python3.13/site-packages/click/termui.py:247
msgid "Error: invalid input"
msgstr ""

#: venv/lib/python3.13/site-packages/click/termui.py:866
msgid "Press any key to continue..."
msgstr ""

#: venv/lib/python3.13/site-packages/click/types.py:332
#, python-brace-format
msgid ""
"Choose from:\n"
"\t{choices}"
msgstr ""

#: venv/lib/python3.13/site-packages/click/types.py:369
msgid "{value!r} is not {choice}."
msgid_plural "{value!r} is not one of {choices}."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.13/site-packages/click/types.py:460
msgid "{value!r} does not match the format {format}."
msgid_plural "{value!r} does not match the formats {formats}."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.13/site-packages/click/types.py:482
msgid "{value!r} is not a valid {number_type}."
msgstr ""

#: venv/lib/python3.13/site-packages/click/types.py:538
#, python-brace-format
msgid "{value} is not in the range {range}."
msgstr ""

#: venv/lib/python3.13/site-packages/click/types.py:679
msgid "{value!r} is not a valid boolean."
msgstr ""

#: venv/lib/python3.13/site-packages/click/types.py:703
msgid "{value!r} is not a valid UUID."
msgstr ""

#: venv/lib/python3.13/site-packages/click/types.py:893
msgid "file"
msgstr ""

#: venv/lib/python3.13/site-packages/click/types.py:895
msgid "directory"
msgstr ""

#: venv/lib/python3.13/site-packages/click/types.py:897
msgid "path"
msgstr ""

#: venv/lib/python3.13/site-packages/click/types.py:944
msgid "{name} {filename!r} does not exist."
msgstr ""

#: venv/lib/python3.13/site-packages/click/types.py:953
msgid "{name} {filename!r} is a file."
msgstr ""

#: venv/lib/python3.13/site-packages/click/types.py:961
msgid "{name} {filename!r} is a directory."
msgstr ""

#: venv/lib/python3.13/site-packages/click/types.py:970
msgid "{name} {filename!r} is not readable."
msgstr ""

#: venv/lib/python3.13/site-packages/click/types.py:979
msgid "{name} {filename!r} is not writable."
msgstr ""

#: venv/lib/python3.13/site-packages/click/types.py:988
msgid "{name} {filename!r} is not executable."
msgstr ""

#: venv/lib/python3.13/site-packages/click/types.py:1055
#, python-brace-format
msgid "{len_type} values are required, but {len_value} was given."
msgid_plural "{len_type} values are required, but {len_value} were given."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.13/site-packages/django/contrib/messages/apps.py:16
msgid "Messages"
msgstr ""

#: venv/lib/python3.13/site-packages/django/contrib/sitemaps/apps.py:8
msgid "Site Maps"
msgstr ""

#: venv/lib/python3.13/site-packages/django/contrib/staticfiles/apps.py:9
msgid "Static Files"
msgstr ""

#: venv/lib/python3.13/site-packages/django/contrib/syndication/apps.py:7
msgid "Syndication"
msgstr ""

#. Translators: String used to replace omitted page numbers in elided page
#. range generated by paginators, e.g. [1, 2, '…', 5, 6, 7, '…', 9, 10].
#: venv/lib/python3.13/site-packages/django/core/paginator.py:30
msgid "…"
msgstr ""

#: venv/lib/python3.13/site-packages/django/core/paginator.py:32
msgid "That page number is not an integer"
msgstr ""

#: venv/lib/python3.13/site-packages/django/core/paginator.py:33
msgid "That page number is less than 1"
msgstr ""

#: venv/lib/python3.13/site-packages/django/core/paginator.py:34
msgid "That page contains no results"
msgstr ""

#: venv/lib/python3.13/site-packages/django/core/validators.py:22
msgid "Enter a valid value."
msgstr ""

#: venv/lib/python3.13/site-packages/django/core/validators.py:70
msgid "Enter a valid domain name."
msgstr ""

#: venv/lib/python3.13/site-packages/django/core/validators.py:153
#: venv/lib/python3.13/site-packages/django/forms/fields.py:775
msgid "Enter a valid URL."
msgstr ""

#: venv/lib/python3.13/site-packages/django/core/validators.py:200
msgid "Enter a valid integer."
msgstr ""

#: venv/lib/python3.13/site-packages/django/core/validators.py:211
msgid "Enter a valid email address."
msgstr ""

#. Translators: "letters" means latin letters: a-z and A-Z.
#: venv/lib/python3.13/site-packages/django/core/validators.py:289
msgid ""
"Enter a valid “slug” consisting of letters, numbers, underscores or hyphens."
msgstr ""

#: venv/lib/python3.13/site-packages/django/core/validators.py:297
msgid ""
"Enter a valid “slug” consisting of Unicode letters, numbers, underscores, or "
"hyphens."
msgstr ""

#: venv/lib/python3.13/site-packages/django/core/validators.py:309
#: venv/lib/python3.13/site-packages/django/core/validators.py:318
#: venv/lib/python3.13/site-packages/django/core/validators.py:332
#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:2220
#, python-format
msgid "Enter a valid %(protocol)s address."
msgstr ""

#: venv/lib/python3.13/site-packages/django/core/validators.py:311
msgid "IPv4"
msgstr ""

#: venv/lib/python3.13/site-packages/django/core/validators.py:320
#: venv/lib/python3.13/site-packages/django/utils/ipv6.py:43
msgid "IPv6"
msgstr ""

#: venv/lib/python3.13/site-packages/django/core/validators.py:334
msgid "IPv4 or IPv6"
msgstr ""

#: venv/lib/python3.13/site-packages/django/core/validators.py:375
msgid "Enter only digits separated by commas."
msgstr ""

#: venv/lib/python3.13/site-packages/django/core/validators.py:381
#, python-format
msgid "Ensure this value is %(limit_value)s (it is %(show_value)s)."
msgstr ""

#: venv/lib/python3.13/site-packages/django/core/validators.py:416
#, python-format
msgid "Ensure this value is less than or equal to %(limit_value)s."
msgstr ""

#: venv/lib/python3.13/site-packages/django/core/validators.py:425
#, python-format
msgid "Ensure this value is greater than or equal to %(limit_value)s."
msgstr ""

#: venv/lib/python3.13/site-packages/django/core/validators.py:434
#, python-format
msgid "Ensure this value is a multiple of step size %(limit_value)s."
msgstr ""

#: venv/lib/python3.13/site-packages/django/core/validators.py:441
#, python-format
msgid ""
"Ensure this value is a multiple of step size %(limit_value)s, starting from "
"%(offset)s, e.g. %(offset)s, %(valid_value1)s, %(valid_value2)s, and so on."
msgstr ""

#: venv/lib/python3.13/site-packages/django/core/validators.py:473
#, python-format
msgid ""
"Ensure this value has at least %(limit_value)d character (it has "
"%(show_value)d)."
msgid_plural ""
"Ensure this value has at least %(limit_value)d characters (it has "
"%(show_value)d)."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.13/site-packages/django/core/validators.py:491
#, python-format
msgid ""
"Ensure this value has at most %(limit_value)d character (it has "
"%(show_value)d)."
msgid_plural ""
"Ensure this value has at most %(limit_value)d characters (it has "
"%(show_value)d)."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.13/site-packages/django/core/validators.py:514
#: venv/lib/python3.13/site-packages/django/forms/fields.py:366
#: venv/lib/python3.13/site-packages/django/forms/fields.py:405
msgid "Enter a number."
msgstr ""

#: venv/lib/python3.13/site-packages/django/core/validators.py:516
#, python-format
msgid "Ensure that there are no more than %(max)s digit in total."
msgid_plural "Ensure that there are no more than %(max)s digits in total."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.13/site-packages/django/core/validators.py:521
#, python-format
msgid "Ensure that there are no more than %(max)s decimal place."
msgid_plural "Ensure that there are no more than %(max)s decimal places."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.13/site-packages/django/core/validators.py:526
#, python-format
msgid ""
"Ensure that there are no more than %(max)s digit before the decimal point."
msgid_plural ""
"Ensure that there are no more than %(max)s digits before the decimal point."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.13/site-packages/django/core/validators.py:597
#, python-format
msgid ""
"File extension “%(extension)s” is not allowed. Allowed extensions are: "
"%(allowed_extensions)s."
msgstr ""

#: venv/lib/python3.13/site-packages/django/core/validators.py:659
msgid "Null characters are not allowed."
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/base.py:1600
#: venv/lib/python3.13/site-packages/django/forms/models.py:908
msgid "and"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/base.py:1602
#, python-format
msgid "%(model_name)s with this %(field_labels)s already exists."
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/constraints.py:22
#, python-format
msgid "Constraint “%(name)s” is violated."
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:134
#, python-format
msgid "Value %(value)r is not a valid choice."
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:135
msgid "This field cannot be null."
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:136
msgid "This field cannot be blank."
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:137
#, python-format
msgid "%(model_name)s with this %(field_label)s already exists."
msgstr ""

#. Translators: The 'lookup_type' is one of 'date', 'year' or
#. 'month'. Eg: "Title must be unique for pub_date year"
#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:141
#, python-format
msgid ""
"%(field_label)s must be unique for %(date_field_label)s %(lookup_type)s."
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:180
#, python-format
msgid "Field of type: %(field_type)s"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:1162
#, python-format
msgid "“%(value)s” value must be either True or False."
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:1163
#, python-format
msgid "“%(value)s” value must be either True, False, or None."
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:1165
msgid "Boolean (Either True or False)"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:1215
#, python-format
msgid "String (up to %(max_length)s)"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:1217
msgid "String (unlimited)"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:1326
msgid "Comma-separated integers"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:1427
#, python-format
msgid ""
"“%(value)s” value has an invalid date format. It must be in YYYY-MM-DD "
"format."
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:1431
#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:1566
#, python-format
msgid ""
"“%(value)s” value has the correct format (YYYY-MM-DD) but it is an invalid "
"date."
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:1435
msgid "Date (without time)"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:1562
#, python-format
msgid ""
"“%(value)s” value has an invalid format. It must be in YYYY-MM-DD "
"HH:MM[:ss[.uuuuuu]][TZ] format."
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:1570
#, python-format
msgid ""
"“%(value)s” value has the correct format (YYYY-MM-DD HH:MM[:ss[.uuuuuu]]"
"[TZ]) but it is an invalid date/time."
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:1575
msgid "Date (with time)"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:1702
#, python-format
msgid "“%(value)s” value must be a decimal number."
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:1704
msgid "Decimal number"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:1864
#, python-format
msgid ""
"“%(value)s” value has an invalid format. It must be in [DD] "
"[[HH:]MM:]ss[.uuuuuu] format."
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:1868
msgid "Duration"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:1920
msgid "Email address"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:1945
msgid "File path"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:2023
#, python-format
msgid "“%(value)s” value must be a float."
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:2025
msgid "Floating point number"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:2065
#, python-format
msgid "“%(value)s” value must be an integer."
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:2067
msgid "Integer"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:2163
msgid "Big (8 byte) integer"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:2180
msgid "Small integer"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:2188
msgid "IPv4 address"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:2219
msgid "IP address"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:2310
#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:2311
#, python-format
msgid "“%(value)s” value must be either None, True or False."
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:2313
msgid "Boolean (Either True, False or None)"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:2364
msgid "Positive big integer"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:2379
msgid "Positive integer"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:2394
msgid "Positive small integer"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:2410
#, python-format
msgid "Slug (up to %(max_length)s)"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:2446
msgid "Text"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:2526
#, python-format
msgid ""
"“%(value)s” value has an invalid format. It must be in HH:MM[:ss[.uuuuuu]] "
"format."
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:2530
#, python-format
msgid ""
"“%(value)s” value has the correct format (HH:MM[:ss[.uuuuuu]]) but it is an "
"invalid time."
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:2534
msgid "Time"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:2642
msgid "URL"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:2666
msgid "Raw binary data"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:2731
#, python-format
msgid "“%(value)s” is not a valid UUID."
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/__init__.py:2733
msgid "Universally unique identifier"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/files.py:244
msgid "File"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/files.py:420
msgid "Image"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/json.py:24
msgid "A JSON object"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/json.py:26
msgid "Value must be valid JSON."
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/related.py:978
#, python-format
msgid "%(model)s instance with %(field)s %(value)r is not a valid choice."
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/related.py:981
msgid "Foreign Key (type determined by related field)"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/related.py:1275
msgid "One-to-one relationship"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/related.py:1332
#, python-format
msgid "%(from)s-%(to)s relationship"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/related.py:1334
#, python-format
msgid "%(from)s-%(to)s relationships"
msgstr ""

#: venv/lib/python3.13/site-packages/django/db/models/fields/related.py:1382
msgid "Many-to-many relationship"
msgstr ""

#. Translators: If found as last label character, these punctuation
#. characters will prevent the default label_suffix to be appended to the label
#: venv/lib/python3.13/site-packages/django/forms/boundfield.py:185
msgid ":?.!"
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/fields.py:95
msgid "This field is required."
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/fields.py:315
msgid "Enter a whole number."
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/fields.py:486
#: venv/lib/python3.13/site-packages/django/forms/fields.py:1267
msgid "Enter a valid date."
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/fields.py:509
#: venv/lib/python3.13/site-packages/django/forms/fields.py:1268
msgid "Enter a valid time."
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/fields.py:536
msgid "Enter a valid date/time."
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/fields.py:570
msgid "Enter a valid duration."
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/fields.py:571
#, python-brace-format
msgid "The number of days must be between {min_days} and {max_days}."
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/fields.py:640
msgid "No file was submitted. Check the encoding type on the form."
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/fields.py:641
msgid "No file was submitted."
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/fields.py:642
msgid "The submitted file is empty."
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/fields.py:644
#, python-format
msgid "Ensure this filename has at most %(max)d character (it has %(length)d)."
msgid_plural ""
"Ensure this filename has at most %(max)d characters (it has %(length)d)."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.13/site-packages/django/forms/fields.py:649
msgid "Please either submit a file or check the clear checkbox, not both."
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/fields.py:717
msgid ""
"Upload a valid image. The file you uploaded was either not an image or a "
"corrupted image."
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/fields.py:889
#: venv/lib/python3.13/site-packages/django/forms/fields.py:975
#: venv/lib/python3.13/site-packages/django/forms/models.py:1592
#, python-format
msgid "Select a valid choice. %(value)s is not one of the available choices."
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/fields.py:977
#: venv/lib/python3.13/site-packages/django/forms/fields.py:1096
#: venv/lib/python3.13/site-packages/django/forms/models.py:1590
msgid "Enter a list of values."
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/fields.py:1097
msgid "Enter a complete value."
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/fields.py:1339
msgid "Enter a valid UUID."
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/fields.py:1369
msgid "Enter a valid JSON."
msgstr ""

#. Translators: This is the default suffix added to form field labels
#: venv/lib/python3.13/site-packages/django/forms/forms.py:97
msgid ":"
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/forms.py:239
#, python-format
msgid "(Hidden field %(name)s) %(error)s"
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/formsets.py:61
#, python-format
msgid ""
"ManagementForm data is missing or has been tampered with. Missing fields: "
"%(field_names)s. You may need to file a bug report if the issue persists."
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/formsets.py:65
#, python-format
msgid "Please submit at most %(num)d form."
msgid_plural "Please submit at most %(num)d forms."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.13/site-packages/django/forms/formsets.py:70
#, python-format
msgid "Please submit at least %(num)d form."
msgid_plural "Please submit at least %(num)d forms."
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.13/site-packages/django/forms/formsets.py:484
#: venv/lib/python3.13/site-packages/django/forms/formsets.py:491
msgid "Order"
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/formsets.py:499
msgid "Delete"
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/models.py:901
#, python-format
msgid "Please correct the duplicate data for %(field)s."
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/models.py:906
#, python-format
msgid "Please correct the duplicate data for %(field)s, which must be unique."
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/models.py:913
#, python-format
msgid ""
"Please correct the duplicate data for %(field_name)s which must be unique "
"for the %(lookup)s in %(date_field)s."
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/models.py:922
msgid "Please correct the duplicate values below."
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/models.py:1359
msgid "The inline value did not match the parent instance."
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/models.py:1450
msgid "Select a valid choice. That choice is not one of the available choices."
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/models.py:1594
#, python-format
msgid "“%(pk)s” is not a valid value."
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/utils.py:229
#, python-format
msgid ""
"%(datetime)s couldn’t be interpreted in time zone %(current_timezone)s; it "
"may be ambiguous or it may not exist."
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/widgets.py:527
msgid "Clear"
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/widgets.py:528
msgid "Currently"
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/widgets.py:529
msgid "Change"
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/widgets.py:866
msgid "Unknown"
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/widgets.py:867
msgid "Yes"
msgstr ""

#: venv/lib/python3.13/site-packages/django/forms/widgets.py:868
msgid "No"
msgstr ""

#. Translators: Please do not add spaces around commas.
#: venv/lib/python3.13/site-packages/django/template/defaultfilters.py:873
msgid "yes,no,maybe"
msgstr ""

#: venv/lib/python3.13/site-packages/django/template/defaultfilters.py:903
#: venv/lib/python3.13/site-packages/django/template/defaultfilters.py:920
#, python-format
msgid "%(size)d byte"
msgid_plural "%(size)d bytes"
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.13/site-packages/django/template/defaultfilters.py:922
#, python-format
msgid "%s KB"
msgstr ""

#: venv/lib/python3.13/site-packages/django/template/defaultfilters.py:924
#, python-format
msgid "%s MB"
msgstr ""

#: venv/lib/python3.13/site-packages/django/template/defaultfilters.py:926
#, python-format
msgid "%s GB"
msgstr ""

#: venv/lib/python3.13/site-packages/django/template/defaultfilters.py:928
#, python-format
msgid "%s TB"
msgstr ""

#: venv/lib/python3.13/site-packages/django/template/defaultfilters.py:930
#, python-format
msgid "%s PB"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dateformat.py:74
msgid "p.m."
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dateformat.py:75
msgid "a.m."
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dateformat.py:80
msgid "PM"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dateformat.py:81
msgid "AM"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dateformat.py:153
msgid "midnight"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dateformat.py:155
msgid "noon"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:7
msgid "Monday"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:8
msgid "Tuesday"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:9
msgid "Wednesday"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:10
msgid "Thursday"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:11
msgid "Friday"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:12
msgid "Saturday"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:13
msgid "Sunday"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:16
msgid "Mon"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:17
msgid "Tue"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:18
msgid "Wed"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:19
msgid "Thu"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:20
msgid "Fri"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:21
msgid "Sat"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:22
msgid "Sun"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:25
msgid "January"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:26
msgid "February"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:27
msgid "March"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:28
msgid "April"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:29
msgid "May"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:30
msgid "June"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:31
msgid "July"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:32
msgid "August"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:33
msgid "September"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:34
msgid "October"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:35
msgid "November"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:36
msgid "December"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:39
msgid "jan"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:40
msgid "feb"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:41
msgid "mar"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:42
msgid "apr"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:43
msgid "may"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:44
msgid "jun"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:45
msgid "jul"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:46
msgid "aug"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:47
msgid "sep"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:48
msgid "oct"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:49
msgid "nov"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:50
msgid "dec"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:53
msgctxt "abbrev. month"
msgid "Jan."
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:54
msgctxt "abbrev. month"
msgid "Feb."
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:55
msgctxt "abbrev. month"
msgid "March"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:56
msgctxt "abbrev. month"
msgid "April"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:57
msgctxt "abbrev. month"
msgid "May"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:58
msgctxt "abbrev. month"
msgid "June"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:59
msgctxt "abbrev. month"
msgid "July"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:60
msgctxt "abbrev. month"
msgid "Aug."
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:61
msgctxt "abbrev. month"
msgid "Sept."
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:62
msgctxt "abbrev. month"
msgid "Oct."
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:63
msgctxt "abbrev. month"
msgid "Nov."
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:64
msgctxt "abbrev. month"
msgid "Dec."
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:67
msgctxt "alt. month"
msgid "January"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:68
msgctxt "alt. month"
msgid "February"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:69
msgctxt "alt. month"
msgid "March"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:70
msgctxt "alt. month"
msgid "April"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:71
msgctxt "alt. month"
msgid "May"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:72
msgctxt "alt. month"
msgid "June"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:73
msgctxt "alt. month"
msgid "July"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:74
msgctxt "alt. month"
msgid "August"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:75
msgctxt "alt. month"
msgid "September"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:76
msgctxt "alt. month"
msgid "October"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:77
msgctxt "alt. month"
msgid "November"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/dates.py:78
msgctxt "alt. month"
msgid "December"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/ipv6.py:20
msgid "This is not a valid IPv6 address."
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/text.py:76
#, python-format
msgctxt "String to return when truncating text"
msgid "%(truncated_text)s…"
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/text.py:287
msgid "or"
msgstr ""

#. Translators: This string is used as a separator between list elements
#: venv/lib/python3.13/site-packages/django/utils/text.py:306
#: venv/lib/python3.13/site-packages/django/utils/timesince.py:135
msgid ", "
msgstr ""

#: venv/lib/python3.13/site-packages/django/utils/timesince.py:8
#, python-format
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.13/site-packages/django/utils/timesince.py:9
#, python-format
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.13/site-packages/django/utils/timesince.py:10
#, python-format
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.13/site-packages/django/utils/timesince.py:11
#, python-format
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.13/site-packages/django/utils/timesince.py:12
#, python-format
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.13/site-packages/django/utils/timesince.py:13
#, python-format
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] ""
msgstr[1] ""

#: venv/lib/python3.13/site-packages/django/views/csrf.py:29
msgid "Forbidden"
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/csrf.py:30
msgid "CSRF verification failed. Request aborted."
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/csrf.py:34
msgid ""
"You are seeing this message because this HTTPS site requires a “Referer "
"header” to be sent by your web browser, but none was sent. This header is "
"required for security reasons, to ensure that your browser is not being "
"hijacked by third parties."
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/csrf.py:40
msgid ""
"If you have configured your browser to disable “Referer” headers, please re-"
"enable them, at least for this site, or for HTTPS connections, or for “same-"
"origin” requests."
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/csrf.py:45
msgid ""
"If you are using the <meta name=\"referrer\" content=\"no-referrer\"> tag or "
"including the “Referrer-Policy: no-referrer” header, please remove them. The "
"CSRF protection requires the “Referer” header to do strict referer checking. "
"If you’re concerned about privacy, use alternatives like <a "
"rel=\"noreferrer\" …> for links to third-party sites."
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/csrf.py:54
msgid ""
"You are seeing this message because this site requires a CSRF cookie when "
"submitting forms. This cookie is required for security reasons, to ensure "
"that your browser is not being hijacked by third parties."
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/csrf.py:60
msgid ""
"If you have configured your browser to disable cookies, please re-enable "
"them, at least for this site, or for “same-origin” requests."
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/csrf.py:66
msgid "More information is available with DEBUG=True."
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/generic/dates.py:44
msgid "No year specified"
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/generic/dates.py:64
#: venv/lib/python3.13/site-packages/django/views/generic/dates.py:115
#: venv/lib/python3.13/site-packages/django/views/generic/dates.py:214
msgid "Date out of range"
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/generic/dates.py:94
msgid "No month specified"
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/generic/dates.py:147
msgid "No day specified"
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/generic/dates.py:194
msgid "No week specified"
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/generic/dates.py:353
#: venv/lib/python3.13/site-packages/django/views/generic/dates.py:384
#, python-format
msgid "No %(verbose_name_plural)s available"
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/generic/dates.py:680
#, python-format
msgid ""
"Future %(verbose_name_plural)s not available because "
"%(class_name)s.allow_future is False."
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/generic/dates.py:720
#, python-format
msgid "Invalid date string “%(datestr)s” given format “%(format)s”"
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/generic/detail.py:56
#, python-format
msgid "No %(verbose_name)s found matching the query"
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/generic/list.py:70
msgid "Page is not “last”, nor can it be converted to an int."
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/generic/list.py:77
#, python-format
msgid "Invalid page (%(page_number)s): %(message)s"
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/generic/list.py:173
#, python-format
msgid "Empty list and “%(class_name)s.allow_empty” is False."
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/static.py:49
msgid "Directory indexes are not allowed here."
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/static.py:51
#, python-format
msgid "“%(path)s” does not exist"
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/static.py:68
#: venv/lib/python3.13/site-packages/django/views/templates/directory_index.html:8
#: venv/lib/python3.13/site-packages/django/views/templates/directory_index.html:11
#, python-format
msgid "Index of %(directory)s"
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/templates/default_urlconf.html:7
#: venv/lib/python3.13/site-packages/django/views/templates/default_urlconf.html:204
msgid "The install worked successfully! Congratulations!"
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/templates/default_urlconf.html:206
#, python-format
msgid ""
"View <a href=\"https://docs.djangoproject.com/en/%(version)s/releases/\" "
"target=\"_blank\" rel=\"noopener\">release notes</a> for Django %(version)s"
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/templates/default_urlconf.html:208
#, python-format
msgid ""
"You are seeing this page because <a href=\"https://docs.djangoproject.com/en/"
"%(version)s/ref/settings/#debug\" target=\"_blank\" "
"rel=\"noopener\">DEBUG=True</a> is in your settings file and you have not "
"configured any URLs."
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/templates/default_urlconf.html:217
msgid "Django Documentation"
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/templates/default_urlconf.html:218
msgid "Topics, references, &amp; how-to’s"
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/templates/default_urlconf.html:226
msgid "Tutorial: A Polling App"
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/templates/default_urlconf.html:227
msgid "Get started with Django"
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/templates/default_urlconf.html:235
msgid "Django Community"
msgstr ""

#: venv/lib/python3.13/site-packages/django/views/templates/default_urlconf.html:236
msgid "Connect, get help, or contribute"
msgstr ""

#: venv/lib/python3.13/site-packages/modeltranslation/widgets.py:32
msgid "None"
msgstr ""
