/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */@layer properties{@supports (((-webkit-hyphens:none)) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,:before,:after,::backdrop{--tw-translate-x:0;--tw-translate-y:0;--tw-translate-z:0;--tw-scale-x:1;--tw-scale-y:1;--tw-scale-z:1;--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-space-y-reverse:0;--tw-space-x-reverse:0;--tw-border-style:solid;--tw-gradient-position:initial;--tw-gradient-from:#0000;--tw-gradient-via:#0000;--tw-gradient-to:#0000;--tw-gradient-stops:initial;--tw-gradient-via-stops:initial;--tw-gradient-from-position:0%;--tw-gradient-via-position:50%;--tw-gradient-to-position:100%;--tw-leading:initial;--tw-font-weight:initial;--tw-shadow:0 0 #0000;--tw-shadow-color:initial;--tw-shadow-alpha:100%;--tw-inset-shadow:0 0 #0000;--tw-inset-shadow-color:initial;--tw-inset-shadow-alpha:100%;--tw-ring-color:initial;--tw-ring-shadow:0 0 #0000;--tw-inset-ring-color:initial;--tw-inset-ring-shadow:0 0 #0000;--tw-ring-inset:initial;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-offset-shadow:0 0 #0000;--tw-duration:initial;--tw-ease:initial}}}@layer theme{:root,:host{--font-sans:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";--font-mono:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;--color-red-300:oklch(80.8% .114 19.571);--color-red-500:oklch(63.7% .237 25.331);--color-red-600:oklch(57.7% .245 27.325);--color-red-700:oklch(50.5% .213 27.518);--color-red-800:oklch(44.4% .177 26.899);--color-orange-200:oklch(90.1% .076 70.697);--color-orange-400:oklch(75% .183 55.934);--color-orange-500:oklch(70.5% .213 47.604);--color-orange-600:oklch(64.6% .222 41.116);--color-amber-50:oklch(98.7% .022 95.277);--color-amber-100:oklch(96.2% .059 95.617);--color-amber-300:oklch(87.9% .169 91.605);--color-amber-400:oklch(82.8% .189 84.429);--color-amber-500:oklch(76.9% .188 70.08);--color-amber-600:oklch(66.6% .179 58.318);--color-amber-700:oklch(55.5% .163 48.998);--color-amber-900:oklch(41.4% .112 45.904);--color-yellow-200:oklch(94.5% .129 101.54);--color-yellow-300:oklch(90.5% .182 98.111);--color-yellow-500:oklch(79.5% .184 86.047);--color-lime-300:oklch(89.7% .196 126.665);--color-lime-400:oklch(84.1% .238 128.85);--color-lime-500:oklch(76.8% .233 130.85);--color-green-100:oklch(96.2% .044 156.743);--color-green-300:oklch(87.1% .15 154.449);--color-green-400:oklch(79.2% .209 151.711);--color-green-500:oklch(72.3% .219 149.579);--color-green-600:oklch(62.7% .194 149.214);--color-green-700:oklch(52.7% .154 150.069);--color-green-800:oklch(44.8% .119 151.328);--color-emerald-50:oklch(97.9% .021 166.113);--color-emerald-500:oklch(69.6% .17 162.48);--color-emerald-600:oklch(59.6% .145 163.225);--color-emerald-700:oklch(50.8% .118 165.612);--color-emerald-800:oklch(43.2% .095 166.913);--color-emerald-900:oklch(37.8% .077 168.94);--color-emerald-950:oklch(26.2% .051 172.552);--color-teal-400:oklch(77.7% .152 181.912);--color-cyan-500:oklch(71.5% .143 215.221);--color-sky-300:oklch(82.8% .111 230.318);--color-sky-400:oklch(74.6% .16 232.661);--color-sky-500:oklch(68.5% .169 237.323);--color-sky-600:oklch(58.8% .158 241.966);--color-sky-700:oklch(50% .134 242.749);--color-sky-800:oklch(44.3% .11 240.79);--color-sky-900:oklch(39.1% .09 240.876);--color-blue-300:oklch(80.9% .105 251.813);--color-blue-400:oklch(70.7% .165 254.624);--color-blue-500:oklch(62.3% .214 259.815);--color-blue-600:oklch(54.6% .245 262.881);--color-blue-700:oklch(48.8% .243 264.376);--color-indigo-400:oklch(67.3% .182 276.935);--color-indigo-500:oklch(58.5% .233 277.117);--color-indigo-600:oklch(51.1% .262 276.966);--color-indigo-700:oklch(45.7% .24 277.023);--color-indigo-900:oklch(35.9% .144 278.697);--color-indigo-950:oklch(25.7% .09 281.288);--color-violet-50:oklch(96.9% .016 293.756);--color-violet-100:oklch(94.3% .029 294.588);--color-violet-400:oklch(70.2% .183 293.541);--color-violet-500:oklch(60.6% .25 292.717);--color-violet-950:oklch(28.3% .141 291.089);--color-purple-400:oklch(71.4% .203 305.504);--color-purple-500:oklch(62.7% .265 303.9);--color-purple-600:oklch(55.8% .288 302.321);--color-purple-800:oklch(43.8% .218 303.724);--color-fuchsia-600:oklch(59.1% .293 322.896);--color-pink-500:oklch(65.6% .241 354.308);--color-rose-400:oklch(71.2% .194 13.428);--color-slate-100:oklch(96.8% .007 247.896);--color-slate-300:oklch(86.9% .022 252.894);--color-slate-400:oklch(70.4% .04 256.788);--color-slate-600:oklch(44.6% .043 257.281);--color-slate-700:oklch(37.2% .044 257.287);--color-slate-800:oklch(27.9% .041 260.031);--color-slate-900:oklch(20.8% .042 265.755);--color-gray-50:oklch(98.5% .002 247.839);--color-gray-100:oklch(96.7% .003 264.542);--color-gray-200:oklch(92.8% .006 264.531);--color-gray-300:oklch(87.2% .01 258.338);--color-gray-400:oklch(70.7% .022 261.325);--color-gray-500:oklch(55.1% .027 264.364);--color-gray-600:oklch(44.6% .03 256.802);--color-gray-700:oklch(37.3% .034 259.733);--color-gray-800:oklch(27.8% .033 256.848);--color-gray-900:oklch(21% .034 264.665);--color-gray-950:oklch(13% .028 261.692);--color-zinc-50:oklch(98.5% 0 0);--color-zinc-100:oklch(96.7% .001 286.375);--color-zinc-200:oklch(92% .004 286.32);--color-zinc-300:oklch(87.1% .006 286.286);--color-zinc-400:oklch(70.5% .015 286.067);--color-zinc-700:oklch(37% .013 285.805);--color-zinc-800:oklch(27.4% .006 286.033);--color-zinc-900:oklch(21% .006 285.885);--color-neutral-50:oklch(98.5% 0 0);--color-neutral-100:oklch(97% 0 0);--color-neutral-200:oklch(92.2% 0 0);--color-neutral-300:oklch(87% 0 0);--color-neutral-400:oklch(70.8% 0 0);--color-neutral-500:oklch(55.6% 0 0);--color-neutral-600:oklch(43.9% 0 0);--color-neutral-700:oklch(37.1% 0 0);--color-neutral-800:oklch(26.9% 0 0);--color-neutral-900:oklch(20.5% 0 0);--color-neutral-950:oklch(14.5% 0 0);--color-stone-50:oklch(98.5% .001 106.423);--color-stone-200:oklch(92.3% .003 48.717);--color-stone-300:oklch(86.9% .005 56.366);--color-stone-700:oklch(37.4% .01 67.558);--color-stone-800:oklch(26.8% .007 34.298);--color-stone-900:oklch(21.6% .006 56.043);--color-stone-950:oklch(14.7% .004 49.25);--color-black:#000;--color-white:#fff;--spacing:.25rem;--container-xs:20rem;--container-sm:24rem;--container-md:28rem;--container-2xl:42rem;--container-3xl:48rem;--container-4xl:56rem;--container-7xl:80rem;--text-xs:.75rem;--text-xs--line-height:calc(1/.75);--text-sm:.875rem;--text-sm--line-height:calc(1.25/.875);--text-lg:1.125rem;--text-lg--line-height:calc(1.75/1.125);--text-xl:1.25rem;--text-xl--line-height:calc(1.75/1.25);--text-2xl:1.5rem;--text-2xl--line-height:calc(2/1.5);--text-3xl:1.875rem;--text-3xl--line-height: 1.2 ;--text-4xl:2.25rem;--text-4xl--line-height:calc(2.5/2.25);--text-5xl:3rem;--text-5xl--line-height:1;--text-6xl:3.75rem;--text-6xl--line-height:1;--font-weight-medium:500;--font-weight-semibold:600;--font-weight-bold:700;--leading-relaxed:1.625;--radius-sm:.25rem;--radius-md:.375rem;--radius-lg:.5rem;--radius-xl:.75rem;--ease-in:cubic-bezier(.4,0,1,1);--ease-out:cubic-bezier(0,0,.2,1);--animate-spin:spin 1s linear infinite;--default-transition-duration:.15s;--default-transition-timing-function:cubic-bezier(.4,0,.2,1);--default-font-family:var(--font-sans);--default-mono-font-family:var(--font-mono);--font-title:"Merriweather",serif;--font-body:"Inter",sans-serif;--color-surface:var(--color-zinc-50);--color-surface-variant:var(--color-zinc-100);--color-on-surface:var(--color-neutral-600);--color-on-surface-variant:var(--color-neutral-500);--color-primary:var(--color-sky-700);--color-on-primary:var(--color-white);--color-secondary:var(--color-neutral-800);--color-outline:var(--color-zinc-300);--color-primary-dark:var(--color-sky-600);--color-success:var(--color-green-700);--color-danger:var(--color-red-700);--radius-radius:var(--radius-lg)}}@layer base{*,:after,:before,::backdrop{box-sizing:border-box;border:0 solid;margin:0;padding:0}::file-selector-button{box-sizing:border-box;border:0 solid;margin:0;padding:0}html,:host{-webkit-text-size-adjust:100%;tab-size:4;line-height:1.5;font-family:var(--default-font-family,ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji");font-feature-settings:var(--default-font-feature-settings,normal);font-variation-settings:var(--default-font-variation-settings,normal);-webkit-tap-highlight-color:transparent}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;-webkit-text-decoration:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:var(--default-mono-font-family,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace);font-feature-settings:var(--default-mono-font-feature-settings,normal);font-variation-settings:var(--default-mono-font-variation-settings,normal);font-size:1em}small{font-size:80%}sub,sup{vertical-align:baseline;font-size:75%;line-height:0;position:relative}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}:-moz-focusring{outline:auto}progress{vertical-align:baseline}summary{display:list-item}ol,ul,menu{list-style:none}img,svg,video,canvas,audio,iframe,embed,object{vertical-align:middle;display:block}img,video{max-width:100%;height:auto}button,input,select,optgroup,textarea{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}::file-selector-button{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}:where(select:is([multiple],[size])) optgroup{font-weight:bolder}:where(select:is([multiple],[size])) optgroup option{padding-inline-start:20px}::file-selector-button{margin-inline-end:4px}::placeholder{opacity:1}@supports (not ((-webkit-appearance:-apple-pay-button))) or (contain-intrinsic-size:1px){::placeholder{color:currentColor}@supports (color:color-mix(in lab,red,red)){::placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}}}textarea{resize:vertical}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-date-and-time-value{min-height:1lh;text-align:inherit}::-webkit-datetime-edit{display:inline-flex}::-webkit-datetime-edit-fields-wrapper{padding:0}::-webkit-datetime-edit{padding-block:0}::-webkit-datetime-edit-year-field{padding-block:0}::-webkit-datetime-edit-month-field{padding-block:0}::-webkit-datetime-edit-day-field{padding-block:0}::-webkit-datetime-edit-hour-field{padding-block:0}::-webkit-datetime-edit-minute-field{padding-block:0}::-webkit-datetime-edit-second-field{padding-block:0}::-webkit-datetime-edit-millisecond-field{padding-block:0}::-webkit-datetime-edit-meridiem-field{padding-block:0}:-moz-ui-invalid{box-shadow:none}button,input:where([type=button],[type=reset],[type=submit]){appearance:button}::file-selector-button{appearance:button}::-webkit-inner-spin-button{height:auto}::-webkit-outer-spin-button{height:auto}[hidden]:where(:not([hidden=until-found])){display:none!important}[data-theme=arctic]{--font-body:"Inter",sans-serif;--font-title:"Inter",sans-serif;--color-surface:var(--color-white);--color-surface-alt:var(--color-slate-100);--color-on-surface:var(--color-slate-700);--color-on-surface-strong:var(--color-black);--color-primary:var(--color-blue-700);--color-on-primary:var(--color-slate-100);--color-secondary:var(--color-indigo-700);--color-on-secondary:var(--color-slate-100);--color-outline:var(--color-slate-300);--color-outline-strong:var(--color-slate-800);--color-surface-dark:var(--color-slate-900);--color-surface-dark-alt:var(--color-slate-800);--color-on-surface-dark:var(--color-slate-300);--color-on-surface-dark-strong:var(--color-white);--color-primary-dark:var(--color-blue-600);--color-on-primary-dark:var(--color-slate-100);--color-secondary-dark:var(--color-indigo-600);--color-on-secondary-dark:var(--color-slate-100);--color-outline-dark:var(--color-slate-700);--color-outline-dark-strong:var(--color-slate-300);--color-info:var(--color-sky-600);--color-on-info:var(--color-white);--color-success:var(--color-green-600);--color-on-success:var(--color-white);--color-warning:var(--color-amber-500);--color-on-warning:var(--color-white);--color-danger:var(--color-red-600);--color-on-danger:var(--color-white);--radius-radius:var(--radius-lg)}[data-theme=minimal]{--font-body:"Montserrat",sans-serif;--font-title:"Montserrat",sans-serif;--color-surface:var(--color-white);--color-surface-alt:var(--color-neutral-100);--color-on-surface:var(--color-neutral-600);--color-on-surface-strong:var(--color-neutral-900);--color-primary:var(--color-black);--color-on-primary:var(--color-neutral-100);--color-secondary:var(--color-neutral-800);--color-on-secondary:var(--color-white);--color-outline:var(--color-neutral-300);--color-outline-strong:var(--color-neutral-800);--color-surface-dark:var(--color-neutral-950);--color-surface-dark-alt:var(--color-neutral-800);--color-on-surface-dark:var(--color-neutral-400);--color-on-surface-dark-strong:var(--color-neutral-100);--color-primary-dark:var(--color-white);--color-on-primary-dark:var(--color-black);--color-secondary-dark:var(--color-neutral-300);--color-on-secondary-dark:var(--color-black);--color-outline-dark:var(--color-neutral-700);--color-outline-dark-strong:var(--color-neutral-300);--color-info:var(--color-sky-500);--color-on-info:var(--color-white);--color-success:var(--color-green-300);--color-on-success:var(--color-slate-900);--color-warning:var(--color-amber-300);--color-on-warning:var(--color-amber-900);--color-danger:var(--color-red-500);--color-on-danger:var(--color-white);--radius-radius:var(--radius-none)}[data-theme=modern]{--font-body:"Lato",sans-serif;--font-title:"Lato",sans-serif;--color-surface:var(--color-white);--color-surface-alt:var(--color-neutral-50);--color-on-surface:var(--color-neutral-600);--color-on-surface-strong:var(--color-neutral-900);--color-primary:var(--color-black);--color-on-primary:var(--color-neutral-100);--color-secondary:var(--color-neutral-800);--color-on-secondary:var(--color-white);--color-outline:var(--color-neutral-300);--color-outline-strong:var(--color-neutral-800);--color-surface-dark:var(--color-neutral-950);--color-surface-dark-alt:var(--color-neutral-900);--color-on-surface-dark:var(--color-neutral-300);--color-on-surface-dark-strong:var(--color-white);--color-primary-dark:var(--color-white);--color-on-primary-dark:var(--color-black);--color-secondary-dark:var(--color-neutral-300);--color-on-secondary-dark:var(--color-black);--color-outline-dark:var(--color-neutral-700);--color-outline-dark-strong:var(--color-neutral-300);--color-info:var(--color-sky-500);--color-on-info:var(--color-white);--color-success:var(--color-green-500);--color-on-success:var(--color-white);--color-warning:var(--color-amber-500);--color-on-warning:var(--color-white);--color-danger:var(--color-red-500);--color-on-danger:var(--color-white);--radius-radius:var(--radius-sm)}[data-theme=high-contrast]{--font-body:"Inter",sans-serif;--font-title:"Inter",sans-serif;--color-surface:var(--color-gray-50);--color-surface-alt:var(--color-gray-200);--color-on-surface:var(--color-gray-800);--color-on-surface-strong:var(--color-gray-950);--color-primary:var(--color-sky-900);--color-on-primary:var(--color-white);--color-secondary:var(--color-indigo-900);--color-on-secondary:var(--color-white);--color-outline:var(--color-gray-500);--color-outline-strong:var(--color-gray-900);--color-surface-dark:var(--color-gray-900);--color-surface-dark-alt:var(--color-gray-800);--color-on-surface-dark:var(--color-gray-300);--color-on-surface-dark-strong:var(--color-gray-100);--color-primary-dark:var(--color-sky-400);--color-on-primary-dark:var(--color-black);--color-secondary-dark:var(--color-indigo-400);--color-on-secondary-dark:var(--color-black);--color-outline-dark:var(--color-gray-500);--color-outline-dark-strong:var(--color-gray-300);--color-info:var(--color-sky-500);--color-on-info:var(--color-black);--color-success:var(--color-green-500);--color-on-success:var(--color-black);--color-warning:var(--color-yellow-500);--color-on-warning:var(--color-black);--color-danger:var(--color-red-500);--color-on-danger:var(--color-black);--radius-radius:var(--radius-sm)}[data-theme=neo-brutalism]{--font-body:"Space Mono",monospace;--font-title:"Montserrat",sans-serif;--color-surface:var(--color-white);--color-surface-alt:var(--color-neutral-50);--color-on-surface:var(--color-black);--color-on-surface-strong:var(--color-black);--color-primary:var(--color-violet-500);--color-on-primary:var(--color-white);--color-secondary:var(--color-lime-400);--color-on-secondary:var(--color-black);--color-outline:var(--color-black);--color-outline-strong:var(--color-black);--color-surface-dark:var(--color-neutral-950);--color-surface-dark-alt:var(--color-neutral-800);--color-on-surface-dark:var(--color-neutral-200);--color-on-surface-dark-strong:var(--color-white);--color-primary-dark:var(--color-violet-400);--color-on-primary-dark:var(--color-black);--color-secondary-dark:var(--color-lime-300);--color-on-secondary-dark:var(--color-black);--color-outline-dark:var(--color-neutral-300);--color-outline-dark-strong:var(--color-white);--color-info:var(--color-sky-600);--color-on-info:var(--color-black);--color-success:var(--color-green-600);--color-on-success:var(--color-black);--color-warning:var(--color-amber-500);--color-on-warning:var(--color-black);--color-danger:var(--color-red-500);--color-on-danger:var(--color-black);--radius-radius:var(--radius-none)}[data-theme=halloween]{--font-body:"Poppins",sans-serif;--font-title:"Denk One",sans-serif;--color-surface:var(--color-white);--color-surface-alt:var(--color-gray-100);--color-on-surface:var(--color-slate-600);--color-on-surface-strong:var(--color-purple-800);--color-primary:var(--color-orange-400);--color-on-primary:var(--color-slate-100);--color-secondary:var(--color-purple-600);--color-on-secondary:var(--color-slate-100);--color-outline:var(--color-gray-200);--color-outline-strong:var(--color-orange-500);--color-surface-dark:var(--color-black);--color-surface-dark-alt:var(--color-gray-900);--color-on-surface-dark:var(--color-violet-100);--color-on-surface-dark-strong:var(--color-white);--color-primary-dark:var(--color-lime-400);--color-on-primary-dark:var(--color-black);--color-secondary-dark:var(--color-fuchsia-600);--color-on-secondary-dark:var(--color-white);--color-outline-dark:var(--color-slate-700);--color-outline-dark-strong:var(--color-purple-600);--color-info:var(--color-sky-500);--color-on-info:var(--color-slate-100);--color-success:var(--color-green-400);--color-on-success:var(--color-slate-900);--color-warning:var(--color-amber-500);--color-on-warning:var(--color-slate-900);--color-danger:var(--color-red-500);--color-on-danger:var(--color-slate-100);--radius-radius:var(--radius-xl)}[data-theme=zombie]{--font-body:"Montserrat",sans-serif;--font-title:"Denk One",sans-serif;--color-surface:var(--color-violet-50);--color-surface-alt:var(--color-violet-100);--color-on-surface:var(--color-slate-600);--color-on-surface-strong:var(--color-purple-800);--color-primary:var(--color-orange-400);--color-on-primary:var(--color-slate-100);--color-secondary:var(--color-purple-600);--color-on-secondary:var(--color-slate-100);--color-outline:var(--color-gray-200);--color-outline-strong:var(--color-slate-800);--color-surface-dark:var(--color-indigo-950);--color-surface-dark-alt:var(--color-violet-950);--color-on-surface-dark:var(--color-violet-100);--color-on-surface-dark-strong:var(--color-white);--color-primary-dark:var(--color-orange-600);--color-on-primary-dark:var(--color-slate-100);--color-secondary-dark:var(--color-lime-500);--color-on-secondary-dark:var(--color-black);--color-outline-dark:var(--color-slate-700);--color-outline-dark-strong:var(--color-slate-400);--color-info:var(--color-sky-500);--color-on-info:var(--color-slate-100);--color-success:var(--color-green-400);--color-on-success:var(--color-slate-900);--color-warning:var(--color-amber-500);--color-on-warning:var(--color-slate-900);--color-danger:var(--color-red-500);--color-on-danger:var(--color-slate-100);--radius-radius:var(--radius-xl)}[data-theme=pastel]{--font-body:"Playpen Sans",cursive;--font-title:"Playpen Sans",cursive;--color-surface:var(--color-amber-50);--color-surface-alt:var(--color-amber-100);--color-on-surface:var(--color-neutral-500);--color-on-surface-strong:var(--color-neutral-700);--color-primary:var(--color-rose-400);--color-on-primary:var(--color-white);--color-secondary:var(--color-orange-200);--color-on-secondary:var(--color-neutral-800);--color-outline:var(--color-neutral-200);--color-outline-strong:var(--color-neutral-500);--color-surface-dark:var(--color-neutral-900);--color-surface-dark-alt:var(--color-neutral-800);--color-on-surface-dark:var(--color-violet-100);--color-on-surface-dark-strong:var(--color-white);--color-primary-dark:var(--color-rose-400);--color-on-primary-dark:var(--color-white);--color-secondary-dark:var(--color-orange-200);--color-on-secondary-dark:var(--color-neutral-800);--color-outline-dark:var(--color-neutral-700);--color-outline-dark-strong:var(--color-neutral-600);--color-info:var(--color-blue-300);--color-on-info:var(--color-sky-800);--color-success:var(--color-green-300);--color-on-success:var(--color-green-800);--color-warning:var(--color-amber-300);--color-on-warning:var(--color-amber-700);--color-danger:var(--color-red-300);--color-on-danger:var(--color-red-800);--radius-radius:var(--radius-xl)}[data-theme="90s"]{--font-body:"Poppins",sans-serif;--font-title:"Oswald",sans-serif;--color-surface:var(--color-neutral-100);--color-surface-alt:var(--color-neutral-200);--color-on-surface:var(--color-neutral-800);--color-on-surface-strong:var(--color-black);--color-primary:var(--color-purple-500);--color-on-primary:var(--color-white);--color-secondary:var(--color-sky-500);--color-on-secondary:var(--color-white);--color-outline:var(--color-neutral-300);--color-outline-strong:var(--color-neutral-800);--color-surface-dark:var(--color-neutral-800);--color-surface-dark-alt:var(--color-neutral-900);--color-on-surface-dark:var(--color-neutral-300);--color-on-surface-dark-strong:var(--color-neutral-100);--color-primary-dark:var(--color-purple-400);--color-on-primary-dark:var(--color-black);--color-secondary-dark:var(--color-blue-400);--color-on-secondary-dark:var(--color-black);--color-outline-dark:var(--color-neutral-700);--color-outline-dark-strong:var(--color-neutral-300);--color-info:var(--color-cyan-500);--color-on-info:var(--color-black);--color-success:var(--color-teal-400);--color-on-success:var(--color-black);--color-warning:var(--color-yellow-300);--color-on-warning:var(--color-black);--color-danger:var(--color-pink-500);--color-on-danger:var(--color-black);--radius-radius:var(--radius-xl)}[data-theme=christmas]{--font-body:"Lato",sans-serif;--font-title:"Jost",sans-serif;--color-surface:var(--color-white);--color-surface-alt:var(--color-emerald-50);--color-on-surface:var(--color-neutral-700);--color-on-surface-strong:var(--color-emerald-800);--color-primary:var(--color-red-600);--color-on-primary:var(--color-white);--color-secondary:var(--color-emerald-700);--color-on-secondary:var(--color-white);--color-outline:var(--color-neutral-300);--color-outline-strong:var(--color-neutral-800);--color-surface-dark:var(--color-emerald-950);--color-surface-dark-alt:var(--color-emerald-800);--color-on-surface-dark:var(--color-neutral-200);--color-on-surface-dark-strong:var(--color-amber-100);--color-primary-dark:var(--color-red-600);--color-on-primary-dark:var(--color-white);--color-secondary-dark:var(--color-emerald-600);--color-on-secondary-dark:var(--color-white);--color-outline-dark:var(--color-emerald-900);--color-outline-dark-strong:var(--color-white);--color-info:var(--color-cyan-500);--color-on-info:var(--color-black);--color-success:var(--color-emerald-500);--color-on-success:var(--color-black);--color-warning:var(--color-amber-500);--color-on-warning:var(--color-black);--color-danger:var(--color-red-500);--color-on-danger:var(--color-black);--radius-radius:var(--radius-md)}[data-theme=prototype]{--font-body:"Playpen Sans",cursive;--font-title:"Playpen Sans",cursive;--color-surface:var(--color-white);--color-surface-alt:var(--color-neutral-100);--color-on-surface:var(--color-black);--color-on-surface-strong:var(--color-black);--color-primary:var(--color-black);--color-on-primary:var(--color-white);--color-secondary:var(--color-neutral-700);--color-on-secondary:var(--color-white);--color-outline:var(--color-black);--color-outline-strong:var(--color-black);--color-surface-dark:var(--color-black);--color-surface-dark-alt:var(--color-neutral-900);--color-on-surface-dark:var(--color-white);--color-on-surface-dark-strong:var(--color-white);--color-primary-dark:var(--color-white);--color-on-primary-dark:var(--color-black);--color-secondary-dark:var(--color-neutral-300);--color-on-secondary-dark:var(--color-black);--color-outline-dark:var(--color-white);--color-outline-dark-strong:var(--color-white);--color-info:var(--color-sky-300);--color-on-info:var(--color-black);--color-success:var(--color-green-300);--color-on-success:var(--color-black);--color-warning:var(--color-yellow-200);--color-on-warning:var(--color-black);--color-danger:var(--color-red-300);--color-on-danger:var(--color-black);--radius-radius:var(--radius-none)}[data-theme=light]{--font-body:"Inter",sans-serif;--font-title:"Merriweather",serif;--color-surface:var(--color-zinc-50);--color-surface-variant:var(--color-zinc-100);--color-on-surface:var(--color-neutral-600);--color-on-surface-variant:var(--color-neutral-500);--color-primary:var(--color-sky-700);--color-on-primary:var(--color-white);--color-secondary:var(--color-neutral-800);--color-on-secondary:var(--color-white);--color-outline:var(--color-zinc-300);--radius-radius:var(--radius-lg)}[data-theme=dark]{--font-body:"Inter",sans-serif;--font-title:"Merriweather",serif;--color-surface:var(--color-zinc-900);--color-surface-variant:var(--color-zinc-800);--color-on-surface:var(--color-zinc-200);--color-on-surface-variant:var(--color-zinc-400);--color-primary:var(--color-sky-600);--color-on-primary:var(--color-white);--color-secondary:var(--color-white);--color-on-secondary:var(--color-black);--color-outline:var(--color-zinc-700);--radius-radius:var(--radius-lg)}[data-theme=industrial]{--font-body:"Poppins",sans-serif;--font-title:"Oswald",sans-serif;--color-surface:var(--color-stone-50);--color-surface-alt:var(--color-stone-200);--color-on-surface:var(--color-stone-800);--color-on-surface-strong:var(--color-black);--color-primary:var(--color-amber-500);--color-on-primary:var(--color-black);--color-secondary:var(--color-stone-900);--color-on-secondary:var(--color-stone-50);--color-outline:var(--color-stone-300);--color-outline-strong:var(--color-blue-600);--color-surface-dark:var(--color-stone-950);--color-surface-dark-alt:var(--color-stone-900);--color-on-surface-dark:var(--color-stone-300);--color-on-surface-dark-strong:var(--color-white);--color-primary-dark:var(--color-amber-400);--color-on-primary-dark:var(--color-black);--color-secondary-dark:var(--color-stone-700);--color-on-secondary-dark:var(--color-white);--color-outline-dark:var(--color-stone-700);--color-outline-dark-strong:var(--color-blue-500);--color-info:var(--color-sky-600);--color-on-info:var(--color-slate-100);--color-success:var(--color-green-600);--color-on-success:var(--color-white);--color-warning:var(--color-amber-500);--color-on-warning:var(--color-black);--color-danger:var(--color-red-600);--color-on-danger:var(--color-white);--radius-radius:var(--radius-none)}}@layer components;@layer utilities{.collapse{visibility:collapse}.visible{visibility:visible}.sr-only{clip:rect(0,0,0,0);white-space:nowrap;border-width:0;width:1px;height:1px;margin:-1px;padding:0;position:absolute;overflow:hidden}.absolute{position:absolute}.relative{position:relative}.static{position:static}.sticky{position:sticky}.top-0{top:calc(var(--spacing)*0)}.top-1\/2{top:50%}.top-4{top:calc(var(--spacing)*4)}.top-24{top:calc(var(--spacing)*24)}.top-full{top:100%}.right-0{right:calc(var(--spacing)*0)}.right-4{right:calc(var(--spacing)*4)}.left-0{left:calc(var(--spacing)*0)}.left-4{left:calc(var(--spacing)*4)}.z-40{z-index:40}.z-50{z-index:50}.mx-1{margin-inline:calc(var(--spacing)*1)}.mx-2{margin-inline:calc(var(--spacing)*2)}.mx-auto{margin-inline:auto}.mt-0\.5{margin-top:calc(var(--spacing)*.5)}.mt-2{margin-top:calc(var(--spacing)*2)}.mt-4{margin-top:calc(var(--spacing)*4)}.mt-6{margin-top:calc(var(--spacing)*6)}.mt-8{margin-top:calc(var(--spacing)*8)}.mt-12{margin-top:calc(var(--spacing)*12)}.mt-16{margin-top:calc(var(--spacing)*16)}.mr-1{margin-right:calc(var(--spacing)*1)}.mr-2{margin-right:calc(var(--spacing)*2)}.mr-3{margin-right:calc(var(--spacing)*3)}.mr-4{margin-right:calc(var(--spacing)*4)}.mb-1{margin-bottom:calc(var(--spacing)*1)}.mb-2{margin-bottom:calc(var(--spacing)*2)}.mb-3{margin-bottom:calc(var(--spacing)*3)}.mb-4{margin-bottom:calc(var(--spacing)*4)}.mb-6{margin-bottom:calc(var(--spacing)*6)}.mb-8{margin-bottom:calc(var(--spacing)*8)}.mb-10{margin-bottom:calc(var(--spacing)*10)}.mb-16{margin-bottom:calc(var(--spacing)*16)}.-ml-1{margin-left:calc(var(--spacing)*-1)}.ml-1{margin-left:calc(var(--spacing)*1)}.ml-2{margin-left:calc(var(--spacing)*2)}.ml-4{margin-left:calc(var(--spacing)*4)}.ml-7{margin-left:calc(var(--spacing)*7)}.block{display:block}.flex{display:flex}.grid{display:grid}.hidden{display:none}.inline{display:inline}.inline-flex{display:inline-flex}.h-3{height:calc(var(--spacing)*3)}.h-4{height:calc(var(--spacing)*4)}.h-5{height:calc(var(--spacing)*5)}.h-6{height:calc(var(--spacing)*6)}.h-8{height:calc(var(--spacing)*8)}.h-12{height:calc(var(--spacing)*12)}.h-16{height:calc(var(--spacing)*16)}.h-20{height:calc(var(--spacing)*20)}.h-24{height:calc(var(--spacing)*24)}.h-32{height:calc(var(--spacing)*32)}.h-48{height:calc(var(--spacing)*48)}.h-64{height:calc(var(--spacing)*64)}.h-96{height:calc(var(--spacing)*96)}.h-full{height:100%}.max-h-48{max-height:calc(var(--spacing)*48)}.w-3{width:calc(var(--spacing)*3)}.w-4{width:calc(var(--spacing)*4)}.w-5{width:calc(var(--spacing)*5)}.w-6{width:calc(var(--spacing)*6)}.w-8{width:calc(var(--spacing)*8)}.w-12{width:calc(var(--spacing)*12)}.w-16{width:calc(var(--spacing)*16)}.w-20{width:calc(var(--spacing)*20)}.w-24{width:calc(var(--spacing)*24)}.w-32{width:calc(var(--spacing)*32)}.w-full{width:100%}.max-w-2xl{max-width:var(--container-2xl)}.max-w-3xl{max-width:var(--container-3xl)}.max-w-4xl{max-width:var(--container-4xl)}.max-w-7xl{max-width:var(--container-7xl)}.max-w-md{max-width:var(--container-md)}.max-w-none{max-width:none}.flex-shrink-0{flex-shrink:0}.-translate-x-8{--tw-translate-x:calc(var(--spacing)*-8);translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-x-0{--tw-translate-x:calc(var(--spacing)*0);translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-x-8{--tw-translate-x:calc(var(--spacing)*8);translate:var(--tw-translate-x)var(--tw-translate-y)}.-translate-y-1\/2{--tw-translate-y: -50% ;translate:var(--tw-translate-x)var(--tw-translate-y)}.scale-95{--tw-scale-x:95%;--tw-scale-y:95%;--tw-scale-z:95%;scale:var(--tw-scale-x)var(--tw-scale-y)}.scale-100{--tw-scale-x:100%;--tw-scale-y:100%;--tw-scale-z:100%;scale:var(--tw-scale-x)var(--tw-scale-y)}.transform{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)}.animate-spin{animation:var(--animate-spin)}.cursor-not-allowed{cursor:not-allowed}.resize{resize:both}.list-inside{list-style-position:inside}.list-disc{list-style-type:disc}.grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.flex-col{flex-direction:column}.flex-wrap{flex-wrap:wrap}.items-center{align-items:center}.items-end{align-items:flex-end}.items-start{align-items:flex-start}.justify-between{justify-content:space-between}.justify-center{justify-content:center}.gap-2{gap:calc(var(--spacing)*2)}.gap-3{gap:calc(var(--spacing)*3)}.gap-4{gap:calc(var(--spacing)*4)}.gap-6{gap:calc(var(--spacing)*6)}.gap-8{gap:calc(var(--spacing)*8)}:where(.space-y-1>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*1)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*1)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-2>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*2)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-3>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*3)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*3)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-4>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*4)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-6>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*6)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*6)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-8>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*8)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*8)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-x-1>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*1)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*1)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-2>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*2)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-3>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*3)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*3)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-4>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*4)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-6>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*6)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*6)*calc(1 - var(--tw-space-x-reverse)))}.overflow-hidden{overflow:hidden}.overflow-x-auto{overflow-x:auto}.overflow-y-auto{overflow-y:auto}.rounded{border-radius:.25rem}.rounded-full{border-radius:3.40282e38px}.rounded-lg{border-radius:var(--radius-lg)}.rounded-md{border-radius:var(--radius-md)}.rounded-radius{border-radius:var(--radius-radius)}.rounded-xl{border-radius:var(--radius-xl)}.rounded-b-radius{border-bottom-right-radius:var(--radius-radius);border-bottom-left-radius:var(--radius-radius)}.border{border-style:var(--tw-border-style);border-width:1px}.border-2{border-style:var(--tw-border-style);border-width:2px}.border-t{border-top-style:var(--tw-border-style);border-top-width:1px}.border-danger\/30{border-color:#bf000f4d}@supports (color:color-mix(in lab,red,red)){.border-danger\/30{border-color:color-mix(in oklab,var(--color-danger)30%,transparent)}}.border-on-primary{border-color:var(--color-on-primary)}.border-outline{border-color:var(--color-outline)}.border-success\/30{border-color:#0081384d}@supports (color:color-mix(in lab,red,red)){.border-success\/30{border-color:color-mix(in oklab,var(--color-success)30%,transparent)}}.border-transparent{border-color:#0000}.border-zinc-300{border-color:var(--color-zinc-300)}.bg-blue-600{background-color:var(--color-blue-600)}.bg-danger\/20{background-color:#bf000f33}@supports (color:color-mix(in lab,red,red)){.bg-danger\/20{background-color:color-mix(in oklab,var(--color-danger)20%,transparent)}}.bg-on-primary{background-color:var(--color-on-primary)}.bg-outline{background-color:var(--color-outline)}.bg-primary{background-color:var(--color-primary)}.bg-sky-700{background-color:var(--color-sky-700)}.bg-success\/20{background-color:#00813833}@supports (color:color-mix(in lab,red,red)){.bg-success\/20{background-color:color-mix(in oklab,var(--color-success)20%,transparent)}}.bg-surface{background-color:var(--color-surface)}.bg-surface-variant{background-color:var(--color-surface-variant)}.bg-surface\/80{background-color:#fafafacc}@supports (color:color-mix(in lab,red,red)){.bg-surface\/80{background-color:color-mix(in oklab,var(--color-surface)80%,transparent)}}.bg-surface\/90{background-color:#fafafae6}@supports (color:color-mix(in lab,red,red)){.bg-surface\/90{background-color:color-mix(in oklab,var(--color-surface)90%,transparent)}}.bg-gradient-to-br{--tw-gradient-position:to bottom right in oklab;background-image:linear-gradient(var(--tw-gradient-stops))}.from-primary\/10{--tw-gradient-from:#0069a41a}@supports (color:color-mix(in lab,red,red)){.from-primary\/10{--tw-gradient-from:color-mix(in oklab,var(--color-primary)10%,transparent)}}.from-primary\/10{--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.to-secondary\/5{--tw-gradient-to:#2626260d}@supports (color:color-mix(in lab,red,red)){.to-secondary\/5{--tw-gradient-to:color-mix(in oklab,var(--color-secondary)5%,transparent)}}.to-secondary\/5{--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.object-cover{object-fit:cover}.p-1{padding:calc(var(--spacing)*1)}.p-2{padding:calc(var(--spacing)*2)}.p-4{padding:calc(var(--spacing)*4)}.p-6{padding:calc(var(--spacing)*6)}.p-8{padding:calc(var(--spacing)*8)}.px-2{padding-inline:calc(var(--spacing)*2)}.px-3{padding-inline:calc(var(--spacing)*3)}.px-4{padding-inline:calc(var(--spacing)*4)}.px-6{padding-inline:calc(var(--spacing)*6)}.px-8{padding-inline:calc(var(--spacing)*8)}.px-12{padding-inline:calc(var(--spacing)*12)}.py-1{padding-block:calc(var(--spacing)*1)}.py-2{padding-block:calc(var(--spacing)*2)}.py-3{padding-block:calc(var(--spacing)*3)}.py-4{padding-block:calc(var(--spacing)*4)}.py-8{padding-block:calc(var(--spacing)*8)}.py-12{padding-block:calc(var(--spacing)*12)}.py-16{padding-block:calc(var(--spacing)*16)}.py-20{padding-block:calc(var(--spacing)*20)}.pt-8{padding-top:calc(var(--spacing)*8)}.text-center{text-align:center}.font-\[\'Merriweather\'\]{font-family:Merriweather}.font-body{font-family:var(--font-body)}.font-sans{font-family:var(--font-sans)}.font-title{font-family:var(--font-title)}.text-2xl{font-size:var(--text-2xl);line-height:var(--tw-leading,var(--text-2xl--line-height))}.text-3xl{font-size:var(--text-3xl);line-height:var(--tw-leading,var(--text-3xl--line-height))}.text-4xl{font-size:var(--text-4xl);line-height:var(--tw-leading,var(--text-4xl--line-height))}.text-lg{font-size:var(--text-lg);line-height:var(--tw-leading,var(--text-lg--line-height))}.text-sm{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.text-xl{font-size:var(--text-xl);line-height:var(--tw-leading,var(--text-xl--line-height))}.text-xs{font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height))}.leading-relaxed{--tw-leading:var(--leading-relaxed);line-height:var(--leading-relaxed)}.font-bold{--tw-font-weight:var(--font-weight-bold);font-weight:var(--font-weight-bold)}.font-medium{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.font-semibold{--tw-font-weight:var(--font-weight-semibold);font-weight:var(--font-weight-semibold)}.text-danger{color:var(--color-danger)}.text-gray-800{color:var(--color-gray-800)}.text-on-primary{color:var(--color-on-primary)}.text-on-primary\/90{color:#ffffffe6}@supports (color:color-mix(in lab,red,red)){.text-on-primary\/90{color:color-mix(in oklab,var(--color-on-primary)90%,transparent)}}.text-on-surface{color:var(--color-on-surface)}.text-on-surface-variant{color:var(--color-on-surface-variant)}.text-primary{color:var(--color-primary)}.text-success{color:var(--color-success)}.text-white{color:var(--color-white)}.opacity-0{opacity:0}.opacity-25{opacity:.25}.opacity-50{opacity:.5}.opacity-75{opacity:.75}.opacity-100{opacity:1}.shadow-lg{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-sm{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring-2{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring-primary{--tw-ring-color:var(--color-primary)}.transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-all{transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-colors{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-shadow{transition-property:box-shadow;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-transform{transition-property:transform,translate,scale,rotate;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.duration-200{--tw-duration:.2s;transition-duration:.2s}.duration-300{--tw-duration:.3s;transition-duration:.3s}.ease-in{--tw-ease:var(--ease-in);transition-timing-function:var(--ease-in)}.ease-out{--tw-ease:var(--ease-out);transition-timing-function:var(--ease-out)}@media (hover:hover){.group-hover\:scale-105:is(:where(.group):hover *){--tw-scale-x:105%;--tw-scale-y:105%;--tw-scale-z:105%;scale:var(--tw-scale-x)var(--tw-scale-y)}.group-hover\:scale-110:is(:where(.group):hover *){--tw-scale-x:110%;--tw-scale-y:110%;--tw-scale-z:110%;scale:var(--tw-scale-x)var(--tw-scale-y)}.group-hover\:text-primary:is(:where(.group):hover *){color:var(--color-primary)}.hover\:bg-black:hover{background-color:var(--color-black)}.hover\:bg-on-primary\/10:hover{background-color:#ffffff1a}@supports (color:color-mix(in lab,red,red)){.hover\:bg-on-primary\/10:hover{background-color:color-mix(in oklab,var(--color-on-primary)10%,transparent)}}.hover\:bg-on-primary\/90:hover{background-color:#ffffffe6}@supports (color:color-mix(in lab,red,red)){.hover\:bg-on-primary\/90:hover{background-color:color-mix(in oklab,var(--color-on-primary)90%,transparent)}}.hover\:bg-outline:hover{background-color:var(--color-outline)}.hover\:bg-primary\/90:hover{background-color:#0069a4e6}@supports (color:color-mix(in lab,red,red)){.hover\:bg-primary\/90:hover{background-color:color-mix(in oklab,var(--color-primary)90%,transparent)}}.hover\:bg-secondary:hover{background-color:var(--color-secondary)}.hover\:bg-surface:hover{background-color:var(--color-surface)}.hover\:bg-surface-variant:hover{background-color:var(--color-surface-variant)}.hover\:text-primary:hover{color:var(--color-primary)}.hover\:text-primary\/80:hover{color:#0069a4cc}@supports (color:color-mix(in lab,red,red)){.hover\:text-primary\/80:hover{color:color-mix(in oklab,var(--color-primary)80%,transparent)}}.hover\:underline:hover{text-decoration-line:underline}.hover\:shadow-xl:hover{--tw-shadow:0 20px 25px -5px var(--tw-shadow-color,#0000001a),0 8px 10px -6px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}.focus\:ring-2:focus{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus\:ring-primary:focus{--tw-ring-color:var(--color-primary)}.focus\:outline-none:focus{--tw-outline-style:none;outline-style:none}@media (min-width:40rem){.sm\:h-56{height:calc(var(--spacing)*56)}.sm\:flex-row{flex-direction:row}.sm\:px-6{padding-inline:calc(var(--spacing)*6)}}@media (min-width:48rem){.md\:block{display:block}.md\:flex{display:flex}.md\:hidden{display:none}.md\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.md\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.md\:grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}:where(.md\:space-x-3>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*3)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*3)*calc(1 - var(--tw-space-x-reverse)))}.md\:text-5xl{font-size:var(--text-5xl);line-height:var(--tw-leading,var(--text-5xl--line-height))}}@media (min-width:64rem){.lg\:col-span-1{grid-column:span 1/span 1}.lg\:col-span-2{grid-column:span 2/span 2}.lg\:col-span-3{grid-column:span 3/span 3}.lg\:mt-0{margin-top:calc(var(--spacing)*0)}.lg\:grid{display:grid}.lg\:hidden{display:none}.lg\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.lg\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.lg\:grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}.lg\:flex-row{flex-direction:row}.lg\:items-center{align-items:center}.lg\:justify-between{justify-content:space-between}.lg\:gap-8{gap:calc(var(--spacing)*8)}.lg\:gap-16{gap:calc(var(--spacing)*16)}.lg\:px-8{padding-inline:calc(var(--spacing)*8)}.lg\:py-24{padding-block:calc(var(--spacing)*24)}.lg\:py-32{padding-block:calc(var(--spacing)*32)}.lg\:text-2xl{font-size:var(--text-2xl);line-height:var(--tw-leading,var(--text-2xl--line-height))}.lg\:text-4xl{font-size:var(--text-4xl);line-height:var(--tw-leading,var(--text-4xl--line-height))}.lg\:text-5xl{font-size:var(--text-5xl);line-height:var(--tw-leading,var(--text-5xl--line-height))}.lg\:text-6xl{font-size:var(--text-6xl);line-height:var(--tw-leading,var(--text-6xl--line-height))}}@media (min-width:80rem){.xl\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}}@media (prefers-color-scheme:dark){.dark\:bg-primary-dark{background-color:var(--color-primary-dark)}}}@property --tw-translate-x{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-y{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-z{syntax:"*";inherits:false;initial-value:0}@property --tw-scale-x{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-y{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-z{syntax:"*";inherits:false;initial-value:1}@property --tw-rotate-x{syntax:"*";inherits:false}@property --tw-rotate-y{syntax:"*";inherits:false}@property --tw-rotate-z{syntax:"*";inherits:false}@property --tw-skew-x{syntax:"*";inherits:false}@property --tw-skew-y{syntax:"*";inherits:false}@property --tw-space-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-space-x-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-gradient-position{syntax:"*";inherits:false}@property --tw-gradient-from{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-via{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-to{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-stops{syntax:"*";inherits:false}@property --tw-gradient-via-stops{syntax:"*";inherits:false}@property --tw-gradient-from-position{syntax:"<length-percentage>";inherits:false;initial-value:0%}@property --tw-gradient-via-position{syntax:"<length-percentage>";inherits:false;initial-value:50%}@property --tw-gradient-to-position{syntax:"<length-percentage>";inherits:false;initial-value:100%}@property --tw-leading{syntax:"*";inherits:false}@property --tw-font-weight{syntax:"*";inherits:false}@property --tw-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-shadow-color{syntax:"*";inherits:false}@property --tw-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-inset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-shadow-color{syntax:"*";inherits:false}@property --tw-inset-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-ring-color{syntax:"*";inherits:false}@property --tw-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-ring-color{syntax:"*";inherits:false}@property --tw-inset-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-ring-inset{syntax:"*";inherits:false}@property --tw-ring-offset-width{syntax:"<length>";inherits:false;initial-value:0}@property --tw-ring-offset-color{syntax:"*";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-duration{syntax:"*";inherits:false}@property --tw-ease{syntax:"*";inherits:false}@keyframes spin{to{transform:rotate(360deg)}}
