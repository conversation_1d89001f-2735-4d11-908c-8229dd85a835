from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from django.views.generic import TemplateView
from django.db.models import Q
from .models import (
    Property,
    PropertyType,
    Location,
    Feature,
    TeamMember,
    Testimonial,
    ContactInquiry,
    City,
)


def home_view(request):
    """Начална страница с препоръчани имоти и отзиви"""
    # Вземаме препоръчани имоти (is_featured=True)
    featured_properties = Property.objects.filter(
        is_published=True, is_featured=True
    ).select_related("location", "property_type", "assigned_broker")[:6]

    # Вземаме активни отзиви
    testimonials = Testimonial.objects.filter(is_active=True)

    # Данни за простата търсачка
    property_types = PropertyType.objects.all().order_by("name")
    locations = City.objects.all().order_by("name")

    context = {
        "featured_properties": featured_properties,
        "testimonials": testimonials,
        "property_types": property_types,
        "locations": locations,
    }
    return render(request, "core/index.html", context)


def property_list_view(request):
    """Страница с всички имоти и филтриране"""
    # Започваме с всички публикувани имоти
    properties = (
        Property.objects.filter(is_published=True)
        .select_related("location", "property_type", "assigned_broker")
        .prefetch_related("features", "images")
    )

    # Вземаме параметрите за филтриране от GET заявката
    property_type = request.GET.get("property_type")
    location_id = request.GET.get("location")  # Вече ще е ID-то на града
    min_price = request.GET.get("min_price")
    max_price = request.GET.get("max_price")
    min_area = request.GET.get("min_area")
    max_area = request.GET.get("max_area")
    bedrooms = request.GET.get("bedrooms")
    features = request.GET.getlist("features")  # Може да има няколко
    search_query = request.GET.get("search")
    sort_by = request.GET.get(
        "sort_by", "-created_at"
    )  # По подразбиране най-нови първо

    # Прилагаме филтрите
    if property_type:
        properties = properties.filter(property_type__slug=property_type)

    if location_id:
        # Търсим всички имоти в кварталите на този град
        properties = properties.filter(location__city__id=location_id)

    if min_price:
        properties = properties.filter(price__gte=min_price)

    if max_price:
        properties = properties.filter(price__lte=max_price)

    if min_area:
        properties = properties.filter(area__gte=min_area)

    if max_area:
        properties = properties.filter(area__lte=max_area)

    if bedrooms:
        properties = properties.filter(bedrooms=bedrooms)

    if features:
        for feature_id in features:
            properties = properties.filter(features__id=feature_id)

    if search_query:
        properties = properties.filter(
            Q(title__icontains=search_query)
            | Q(description__icontains=search_query)
            | Q(location__name__icontains=search_query)
            | Q(location__city__name__icontains=search_query)
        )

    # Прилагаме сортирането
    if sort_by in ["price", "-price", "area", "-area", "created_at", "-created_at"]:
        properties = properties.order_by(sort_by)

    # Данни за филтрите (за попълване на формата)
    property_types = PropertyType.objects.all()
    locations = City.objects.all()  # Градове за филтъра
    all_features = Feature.objects.all()

    context = {
        "properties": properties,
        "property_types": property_types,
        "locations": locations,
        "all_features": all_features,
        "total_count": properties.count(),
        # Текущите стойности на филтрите за запазване в формата
        "current_filters": {
            "property_type": property_type,
            "location": location_id,
            "min_price": min_price,
            "max_price": max_price,
            "min_area": min_area,
            "max_area": max_area,
            "bedrooms": bedrooms,
            "features": features,
            "search": search_query,
            "sort_by": sort_by,
        },
    }

    # За htmx заявки връщаме само резултатите
    if request.headers.get("HX-Request"):
        response = render(request, "partials/property_list_results.html", context)
        # Добавяме общия брой имоти в header-а за JavaScript да може да го използва
        response["X-Total-Count"] = str(context["total_count"])
        return response

    # За обичайни заявки връщаме пълната страница
    return render(request, "core/property_list.html", context)


def property_detail_view(request, slug):
    """Детайлна страница за конкретен имот"""
    property_obj = get_object_or_404(
        Property.objects.select_related(
            "location", "property_type", "assigned_broker"
        ).prefetch_related("features", "images"),
        slug=slug,
        is_published=True,
    )

    # Подобни имоти (същия тип и локация, но не същия имот)
    similar_properties = (
        Property.objects.filter(
            property_type=property_obj.property_type,
            location=property_obj.location,
            is_published=True,
        )
        .exclude(id=property_obj.id)
        .select_related("location", "property_type")[:4]
    )

    context = {
        "property": property_obj,
        "similar_properties": similar_properties,
    }
    return render(request, "core/property_detail.html", context)


def contact_inquiry_create_view(request):
    """Обработва POST заявки от формите за контакт"""
    if request.method == "POST":
        name = request.POST.get("name")
        email = request.POST.get("email")
        phone = request.POST.get("phone")
        message = request.POST.get("message")
        property_id = request.POST.get(
            "property_id"
        )  # Може да е None за общи запитвания

        # Основна валидация
        if not all([name, email, message]):
            return JsonResponse(
                {
                    "success": False,
                    "error": "Моля попълнете всички задължителни полета.",
                }
            )

        # Създаваме запитването
        inquiry_data = {
            "name": name,
            "email": email,
            "phone": phone or "",
            "message": message,
        }

        if property_id:
            try:
                property_obj = Property.objects.get(id=property_id, is_published=True)
                inquiry_data["property"] = property_obj
            except Property.DoesNotExist:
                pass

        ContactInquiry.objects.create(**inquiry_data)

        return JsonResponse(
            {
                "success": True,
                "message": "Благодарим за запитването! Ще се свържем с Вас скоро.",
            }
        )

    # За GET заявки или невалидни методи
    return JsonResponse({"success": False, "error": "Невалиден метод"})


# Статични страници като class-based views
class AboutView(TemplateView):
    """За нас страница"""

    template_name = "core/about.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["team_members"] = TeamMember.objects.all()
        return context


class ContactView(TemplateView):
    """Контакти страница"""

    template_name = "core/contact.html"


class PrivacyPolicyView(TemplateView):
    """Политика за поверителност"""

    template_name = "core/privacy_policy.html"


class TermsOfServiceView(TemplateView):
    """Общи условия"""

    template_name = "core/terms_of_service.html"
