from django.test import TestCase
from core.models import City, Location, PropertyType, Feature
from core.models import Property, PropertyImage, ContactInquiry, TeamMember, Testimonial


class ModelSmokeTests(TestCase):
    def test_city_model_creation(self):
        """Тества дали моделът City се създава успешно."""
        city = City.objects.create(name="Тестов Град")
        self.assertEqual(city.name, "Тестов Град")
        self.assertEqual(str(city), "Тестов Град")

    def test_location_model_creation(self):
        """Тества дали моделът Location се създава успешно."""
        city = City.objects.create(name="Тестов Град")
        location = Location.objects.create(name="Тестови Квартал", city=city)
        self.assertEqual(location.name, "Тестови Квартал")
        self.assertEqual(location.city.name, "Тестов Град")
        self.assertEqual(str(location), "Тестови Квартал, Тестов Град")

    def test_property_type_model_creation(self):
        """Тества дали моделът PropertyType се създава успешно."""
        prop_type = PropertyType.objects.create(name="Тестов Тип")
        self.assertEqual(prop_type.name, "Тестов Тип")
        self.assertEqual(str(prop_type), "Тестов Тип")

    def test_feature_model_creation(self):
        """Тества дали моделът Feature се създава успешно."""
        feature = Feature.objects.create(name="Тестова Особеност")
        self.assertEqual(feature.name, "Тестова Особеност")
        self.assertEqual(str(feature), "Тестова Особеност")

    def test_team_member_model_creation(self):
        """Тества дали моделът TeamMember се създава успешно."""
        member = TeamMember.objects.create(
            name="Иван Иванов",
            phone="0888123456",
            email="<EMAIL>",
            title="Брокер",
        )
        self.assertEqual(member.name, "Иван Иванов")
        self.assertEqual(str(member), "Иван Иванов - Брокер")

    def test_testimonial_model_creation(self):
        """Тества дали моделът Testimonial се създава успешно."""
        testimonial = Testimonial.objects.create(
            client_name="Петър Петров", quote="Страхотно обслужване!", is_active=True
        )
        self.assertEqual(testimonial.client_name, "Петър Петров")
        self.assertTrue(testimonial.is_active)
        self.assertEqual(str(testimonial), "Отзив от Петър Петров")


class PropertyModelTests(TestCase):
    def setUp(self):
        """Създава необходимите обекти за тестовете на Property."""
        self.city = City.objects.create(name="София")
        self.location = Location.objects.create(name="Център", city=self.city)
        self.property_type = PropertyType.objects.create(name="Апартамент")
        self.broker = TeamMember.objects.create(name="Тестов Брокер")
        self.feature1 = Feature.objects.create(name="Гараж")
        self.feature2 = Feature.objects.create(name="Асансьор")

    def test_property_model_creation(self):
        """Тества дали основният модел Property се създава успешно с всички релации."""
        prop = Property.objects.create(
            title="Луксозен апартамент в центъра",
            property_type=self.property_type,
            location=self.location,
            price=150000.00,
            area=120,
            bedrooms=2,
            description="Просторен и светъл апартамент.",
            assigned_broker=self.broker,
            is_published=True,
            is_featured=True,
        )
        prop.features.add(self.feature1, self.feature2)

        self.assertEqual(prop.title, "Луксозен апартамент в центъра")
        self.assertEqual(prop.property_type.name, "Апартамент")
        self.assertEqual(prop.location.name, "Център")
        self.assertEqual(prop.location.city.name, "София")
        self.assertEqual(prop.assigned_broker.name, "Тестов Брокер")
        self.assertTrue(prop.is_published)
        self.assertTrue(prop.is_featured)
        self.assertEqual(prop.features.count(), 2)
        self.assertIn(self.feature1, prop.features.all())
        self.assertEqual(str(prop), "Луксозен апартамент в центъра")

    def test_property_image_model_creation(self):
        """Тества създаването на PropertyImage и връзката му с Property."""
        prop = Property.objects.create(
            title="Тестов имот за снимки",
            property_type=self.property_type,
            location=self.location,
            price=1000,
            area=50,
            assigned_broker=self.broker,
        )
        image = PropertyImage.objects.create(
            property=prop, image="uploads/test_image.jpg", order=1
        )
        self.assertEqual(image.property.title, "Тестов имот за снимки")
        self.assertEqual(image.order, 1)
        self.assertEqual(Property.objects.first().images.count(), 1)
        self.assertEqual(str(image), f"Снимка {image.order} - {prop.title}")

    def test_contact_inquiry_creation(self):
        """Тества създаването на ContactInquiry."""
        inquiry = ContactInquiry.objects.create(
            name="Тестов Клиент", email="<EMAIL>", message="Интересувам се."
        )
        self.assertEqual(inquiry.name, "Тестов Клиент")
        self.assertFalse(inquiry.is_handled)
        self.assertEqual(str(inquiry), "Общо запитване от Тестов Клиент")
