from django.test import TestCase, Client
from django.urls import reverse
from core.models import Property, City, Location, PropertyType, TeamMember, Testimonial


class ViewsTest(TestCase):

    def setUp(self):
        """Създаване на тестови данни, които ще се използват в различните тестове за изгледи."""
        self.client = Client()
        self.city = City.objects.create(name="Тестов град")
        self.location = Location.objects.create(name="Тестова локация", city=self.city)
        self.property_type = PropertyType.objects.create(name="Тестов тип")
        self.broker = TeamMember.objects.create(
            name="Тестов брокер", email="<EMAIL>", phone="123"
        )

        # Създаване на препоръчан имот
        self.featured_property = Property.objects.create(
            title="Препоръчан имот",
            slug="featured-property",
            location=self.location,
            property_type=self.property_type,
            price=200000,
            area=150,
            assigned_broker=self.broker,
            is_featured=True,
            is_published=True,
        )

        # Създаване на обикновен имот (непрепоръчан)
        self.regular_property = Property.objects.create(
            title="Обикновен имот",
            slug="regular-property",
            location=self.location,
            property_type=self.property_type,
            price=100000,
            area=80,
            assigned_broker=self.broker,
            is_published=True,
        )

        # Създаване на активен отзив
        self.testimonial = Testimonial.objects.create(
            client_name="Доволен Клиент", quote="Супер са!", is_active=True
        )

    def test_home_view_get(self):
        """Тества дали началната страница се зарежда успешно и използва правилния шаблон."""
        response = self.client.get(reverse("core:home"))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, "core/index.html")

    def test_home_view_context_data(self):
        """Тества дали началната страница получава правилните данни в контекста."""
        response = self.client.get(reverse("core:home"))
        self.assertEqual(response.status_code, 200)

        # Проверява дали препоръчаният имот е в контекста
        self.assertIn(self.featured_property, response.context["featured_properties"])
        # Проверява дали обикновеният имот НЕ е в контекста на препоръчаните
        self.assertNotIn(self.regular_property, response.context["featured_properties"])
        # Проверява дали отзивът е в контекста
        self.assertIn(self.testimonial, response.context["testimonials"])

    def test_property_list_view_get(self):
        """Тества дали страницата със списък с имоти се зарежда и съдържа всички имоти."""
        response = self.client.get(reverse("core:property-list"))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, "core/property_list.html")
        self.assertIn("properties", response.context)
        self.assertIn("total_count", response.context)

    def test_property_detail_view_get(self):
        """Тества дали детайлната страница на имот се зарежда с правилните данни."""
        url = reverse(
            "core:property-detail", kwargs={"slug": self.featured_property.slug}
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, "core/property_detail.html")
        self.assertEqual(response.context["property"], self.featured_property)

    def test_static_pages_load_successfully(self):
        """Тества дали всички статични страници се зареждат успешно."""
        static_pages = [
            "core:about",
            "core:contact",
            "core:privacy-policy",
            "core:terms-of-service",
        ]
        for page in static_pages:
            url = reverse(page)
            response = self.client.get(url)
            self.assertEqual(response.status_code, 200, f"Failed to load page: {page}")

    def test_contact_inquiry_post(self):
        """Тества успешното изпращане на формата за запитване."""

        from core.models import ContactInquiry

        initial_inquiry_count = ContactInquiry.objects.count()

        contact_data = {
            "name": "Тестов Потребител",
            "email": "<EMAIL>",
            "phone": "0899123456",
            "message": "Това е тестово съобщение.",
        }

        response = self.client.post(
            reverse("core:contact-inquiry-create"), contact_data
        )

        # Проверява дали заявката е успешна и броят на запитванията се е увеличил
        self.assertEqual(response.status_code, 200)
        self.assertEqual(ContactInquiry.objects.count(), initial_inquiry_count + 1)

        # Проверява дали данните са записани правилно
        new_inquiry = ContactInquiry.objects.latest("created_at")
        self.assertEqual(new_inquiry.name, "Тестов Потребител")
        self.assertEqual(new_inquiry.email, "<EMAIL>")
