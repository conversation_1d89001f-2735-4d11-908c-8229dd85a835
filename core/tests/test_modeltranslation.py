"""
Тестове за django-modeltranslation функционалност
"""

from django.test import TestCase
from django.utils.translation import activate, deactivate
from core.models import (
    City,
    Location,
    PropertyType,
    Feature,
    Property,
    Testimonial,
    TeamMember,
)


class ModelTranslationTests(TestCase):
    """Тестове за правилната работа на django-modeltranslation"""

    def setUp(self):
        """Настройка на тестови данни"""
        self.city = City.objects.create(name="София")
        self.location = Location.objects.create(name="Център", city=self.city)
        self.property_type = PropertyType.objects.create(
            name="Апартамент", slug="apartment"
        )
        self.feature = Feature.objects.create(name="Гараж")
        self.broker = TeamMember.objects.create(
            name="Тест Брокер",
            phone="123456789",
            email="<EMAIL>",
            title="Консултант",
        )

    def test_city_translation(self):
        """Тества преводите за модела City"""
        # Проверява български превод
        activate("bg")
        self.assertEqual(self.city.name, "София")

        # Задава английски превод
        activate("en")
        self.city.name = "Sofia"
        self.city.save()

        # Проверява английския превод
        self.assertEqual(self.city.name, "Sofia")

        # Проверява, че българският превод е запазен
        activate("bg")
        self.assertEqual(self.city.name, "София")

        deactivate()

    def test_location_translation(self):
        """Тества преводите за модела Location"""
        activate("bg")
        self.assertEqual(self.location.name, "Център")

        activate("en")
        self.location.name = "Center"
        self.location.save()

        self.assertEqual(self.location.name, "Center")

        activate("bg")
        self.assertEqual(self.location.name, "Център")

        deactivate()

    def test_property_type_translation(self):
        """Тества преводите за модела PropertyType"""
        activate("bg")
        self.assertEqual(self.property_type.name, "Апартамент")

        activate("en")
        self.property_type.name = "Apartment"
        self.property_type.save()

        self.assertEqual(self.property_type.name, "Apartment")

        activate("bg")
        self.assertEqual(self.property_type.name, "Апартамент")

        deactivate()

    def test_feature_translation(self):
        """Тества преводите за модела Feature"""
        activate("bg")
        self.assertEqual(self.feature.name, "Гараж")

        activate("en")
        self.feature.name = "Garage"
        self.feature.save()

        self.assertEqual(self.feature.name, "Garage")

        activate("bg")
        self.assertEqual(self.feature.name, "Гараж")

        deactivate()

    def test_property_translation(self):
        """Тества преводите за модела Property"""
        property_obj = Property.objects.create(
            title="Тестов Имот",
            slug="testov-imot",
            location=self.location,
            property_type=self.property_type,
            price=200000,
            area=100,
            description="Описание на тестовия имот",
            assigned_broker=self.broker,
            is_published=True,
        )

        activate("bg")
        self.assertEqual(property_obj.title, "Тестов Имот")
        self.assertEqual(property_obj.description, "Описание на тестовия имот")

        activate("en")
        property_obj.title = "Test Property"
        property_obj.description = "Description of the test property"
        property_obj.save()

        self.assertEqual(property_obj.title, "Test Property")
        self.assertEqual(property_obj.description, "Description of the test property")

        activate("bg")
        self.assertEqual(property_obj.title, "Тестов Имот")
        self.assertEqual(property_obj.description, "Описание на тестовия имот")

        deactivate()

    def test_testimonial_translation(self):
        """Тества преводите за модела Testimonial"""
        testimonial = Testimonial.objects.create(
            client_name="Иван Петров", quote="Отличен сервиз!", is_active=True
        )

        activate("bg")
        self.assertEqual(testimonial.client_name, "Иван Петров")
        self.assertEqual(testimonial.quote, "Отличен сервиз!")

        activate("en")
        testimonial.client_name = "Ivan Petrov"
        testimonial.quote = "Excellent service!"
        testimonial.save()

        self.assertEqual(testimonial.client_name, "Ivan Petrov")
        self.assertEqual(testimonial.quote, "Excellent service!")

        activate("bg")
        self.assertEqual(testimonial.client_name, "Иван Петров")
        self.assertEqual(testimonial.quote, "Отличен сервиз!")

        deactivate()

    def test_translation_fields_exist(self):
        """Тества дали преводимите полета съществуват в базата данни"""
        from django.db import connection

        with connection.cursor() as cursor:
            # Проверява дали City има translation полета
            cursor.execute("PRAGMA table_info(core_city)")
            columns = [row[1] for row in cursor.fetchall()]

            self.assertIn("name_bg", columns)
            self.assertIn("name_en", columns)
            self.assertIn("name_el", columns)
            self.assertIn("name_ru", columns)

    def test_all_registered_models_have_translation_options(self):
        """Тества дали всички регистрирани модели имат правилни translation options"""
        from modeltranslation.translator import translator

        # Проверява дали моделите са регистрирани
        registered_models = [opts.model for opts in translator._registry.values()]

        self.assertIn(City, registered_models)
        self.assertIn(Location, registered_models)
        self.assertIn(PropertyType, registered_models)
        self.assertIn(Feature, registered_models)
        self.assertIn(Property, registered_models)
        self.assertIn(Testimonial, registered_models)

    def tearDown(self):
        """Почистване след тестовете"""
        deactivate()
