"""
Тестове за автоматично генериране на slug-ове с поддръжка за Unicode символи
"""

from django.test import TestCase
from django.utils import translation
from core.models import Property, PropertyType, Location, TeamMember, City


class AutoSlugTests(TestCase):
    """Тестове за автоматично генериране на slug-ове"""

    def setUp(self):
        """Подготовка на тестови данни"""
        # Създаване на град
        self.city = City.objects.create(name_bg="София", name_en="Sofia")

        # Създаване на локация
        self.location = Location.objects.create(
            name_bg="Център", name_en="Center", city=self.city
        )

        # Създаване на тип имот
        self.property_type = PropertyType.objects.create(
            name_bg="Апартамент", name_en="Apartment"
        )

        # Създаване на брокер
        self.broker = TeamMember.objects.create(
            name_bg="Тест Брокер",
            name_en="Test Broker",
            title_bg="Консултант",
            title_en="Consultant",
            phone="123456789",
            email="<EMAIL>",
        )

    def test_property_slug_generation_english(self):
        """Тест за генериране на slug от английски заглавия"""
        property_obj = Property.objects.create(
            title="Test Property in Center",
            property_type=self.property_type,
            location=self.location,
            price=200000.00,
            area=100,
            bedrooms=2,
            description="Test property description",
            assigned_broker=self.broker,
        )

        # Проверяваме че slug-ът се генерира автоматично
        self.assertIsNotNone(property_obj.slug)
        self.assertTrue(len(property_obj.slug) > 0)
        expected_slug = "test-property-in-center"
        self.assertEqual(property_obj.slug, expected_slug)

        print(f"English slug: {property_obj.slug}")

    def test_property_slug_generation_bulgarian(self):
        """Тест за генериране на slug от български заглавия (кирилица)"""
        property_obj = Property.objects.create(
            title="Тестов Имот в Центъра",
            property_type=self.property_type,
            location=self.location,
            price=200000.00,
            area=100,
            bedrooms=2,
            description="Описание на тестовия имот",
            assigned_broker=self.broker,
        )

        # Проверяваме че slug-ът се генерира автоматично
        self.assertIsNotNone(property_obj.slug)
        self.assertTrue(len(property_obj.slug) > 0)

        # Проверяваме че slug-ът е URL-безопасен
        self.assertNotIn(" ", property_obj.slug)
        # Note: Default django slugify doesn't handle Cyrillic well
        # but at least it creates a valid slug

        print(f"Bulgarian slug: {property_obj.slug}")

    def test_property_slug_uniqueness(self):
        """Тест за уникалност на slug-овете"""
        # Създаване на първи имот
        property1 = Property.objects.create(
            title="Test Property",
            property_type=self.property_type,
            location=self.location,
            price=200000.00,
            area=100,
            bedrooms=2,
            description="Test description",
            assigned_broker=self.broker,
        )

        # Създаване на втори имот със същото заглавие
        property2 = Property.objects.create(
            title="Test Property",
            property_type=self.property_type,
            location=self.location,
            price=300000.00,
            area=120,
            bedrooms=3,
            description="Another test description",
            assigned_broker=self.broker,
        )

        # Проверяваме че slug-овете са различни
        self.assertNotEqual(property1.slug, property2.slug)

        print(f"First property slug: {property1.slug}")
        print(f"Second property slug: {property2.slug}")

    def test_property_type_slug_generation_simple(self):
        """Тест за генериране на slug за тип имот с различни имена"""
        # Тестване с английски
        pt_en = PropertyType.objects.create(name_en="House", name_bg="Къща")
        self.assertIsNotNone(pt_en.slug)

        # Ще използваме различно име за втория тип
        pt_villa = PropertyType.objects.create(name_en="Villa", name_bg="Вила")
        self.assertIsNotNone(pt_villa.slug)

        print(f"PropertyType House slug: {pt_en.slug}")
        print(f"PropertyType Villa slug: {pt_villa.slug}")

    def test_slug_no_update_on_title_change(self):
        """Тест че slug-ът не се обновява при промяна на заглавието (always_update=False)"""
        property_obj = Property.objects.create(
            title="Original Title",
            property_type=self.property_type,
            location=self.location,
            price=200000.00,
            area=100,
            bedrooms=2,
            description="Test description",
            assigned_broker=self.broker,
        )

        original_slug = property_obj.slug

        # Промяна на заглавието
        property_obj.title = "New Title"
        property_obj.save()

        # Slug-ът трябва да остане същия
        property_obj.refresh_from_db()
        self.assertEqual(property_obj.slug, original_slug)

        print(f"Original slug preserved: {property_obj.slug}")

    def test_slug_url_safety(self):
        """Тест за URL безопасност на генерираните slug-ове"""
        property_obj = Property.objects.create(
            title="Property with Special Characters & Spaces!",
            property_type=self.property_type,
            location=self.location,
            price=200000.00,
            area=100,
            bedrooms=2,
            description="Test description",
            assigned_broker=self.broker,
        )

        # Проверяваме че slug-ът е безопасен за URL
        self.assertNotIn(" ", property_obj.slug)
        self.assertNotIn("&", property_obj.slug)
        self.assertNotIn("!", property_obj.slug)
        self.assertRegex(property_obj.slug, r"^[a-z0-9-]+$")

        print(f"URL-safe slug: {property_obj.slug}")

    def test_autoslug_basic_functionality(self):
        """Основен тест за функционалността на AutoSlugField"""
        property_obj = Property.objects.create(
            title="My Amazing Property",
            property_type=self.property_type,
            location=self.location,
            price=200000.00,
            area=100,
            bedrooms=2,
            description="Amazing property description",
            assigned_broker=self.broker,
        )

        # Тест 1: Slug се генерира автоматично
        self.assertIsNotNone(property_obj.slug)
        self.assertEqual(property_obj.slug, "my-amazing-property")

        # Тест 2: Slug е уникален
        property_obj2 = Property.objects.create(
            title="My Amazing Property",  # Същото заглавие
            property_type=self.property_type,
            location=self.location,
            price=300000.00,
            area=120,
            bedrooms=3,
            description="Another property description",
            assigned_broker=self.broker,
        )

        self.assertNotEqual(property_obj.slug, property_obj2.slug)
        self.assertTrue(property_obj2.slug.startswith("my-amazing-property"))

        print(f"Original: {property_obj.slug}")
        print(f"Duplicate: {property_obj2.slug}")

    def test_modeltranslation_integration(self):
        """Тест за интеграция с modeltranslation"""
        # Създаване с различни езикови версии
        property_obj = Property.objects.create(
            title_bg="Български Заглавие",
            title_en="English Title",
            property_type=self.property_type,
            location=self.location,
            price=200000.00,
            area=100,
            bedrooms=2,
            description="Test description",
            assigned_broker=self.broker,
        )

        # Основният slug трябва да се генерира
        self.assertIsNotNone(property_obj.slug)

        # Можем да проверим дали има language-specific slug полета
        print(f"Main slug: {property_obj.slug}")

        # Проверяваме дали има specific language slugs
        if hasattr(property_obj, "slug_bg"):
            print(f"Bulgarian slug: {property_obj.slug_bg}")
        if hasattr(property_obj, "slug_en"):
            print(f"English slug: {property_obj.slug_en}")

        # Основният тест е че поне един slug се генерира
        self.assertTrue(len(property_obj.slug) > 0)
