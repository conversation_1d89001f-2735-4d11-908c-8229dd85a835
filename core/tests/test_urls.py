from django.test import SimpleTestCase
from django.urls import reverse, resolve
from core.views import (
    home_view,
    property_list_view,
    property_detail_view,
    contact_inquiry_create_view,
    AboutView,
    ContactView,
    PrivacyPolicyView,
    TermsOfServiceView,
)


class UrlsTest(SimpleTestCase):

    def test_home_url_resolves(self):
        """Тества дали URL адресът за началната страница се свързва с правилния изглед."""
        url = reverse("core:home")
        self.assertEqual(resolve(url).func, home_view)

    def test_property_list_url_resolves(self):
        """Тества дали URL адресът за списъка с имоти се свързва с правилния изглед."""
        url = reverse("core:property-list")
        self.assertEqual(resolve(url).func, property_list_view)

    def test_property_detail_url_resolves(self):
        """Тества дали URL адресът за детайл на имот се свързва с правилния изглед."""
        url = reverse("core:property-detail", args=["some-slug"])
        self.assertEqual(resolve(url).func, property_detail_view)

    def test_contact_inquiry_url_resolves(self):
        """Тества дали URL адресът за създаване на запитване се свързва с правилния изглед."""
        url = reverse("core:contact-inquiry-create")
        self.assertEqual(resolve(url).func, contact_inquiry_create_view)

    def test_about_url_resolves(self):
        """Тества дали URL адресът за 'За нас' се свързва с правилния изглед."""
        url = reverse("core:about")
        self.assertEqual(resolve(url).func.view_class, AboutView)

    def test_contact_url_resolves(self):
        """Тества дали URL адресът за 'Контакти' се свързва с правилния изглед."""
        url = reverse("core:contact")
        self.assertEqual(resolve(url).func.view_class, ContactView)

    def test_privacy_policy_url_resolves(self):
        """Тества дали URL адресът за 'Политика за поверителност' се свързва с правилния изглед."""
        url = reverse("core:privacy-policy")
        self.assertEqual(resolve(url).func.view_class, PrivacyPolicyView)

    def test_terms_of_service_url_resolves(self):
        """Тества дали URL адресът за 'Общи условия' се свързва с правилния изглед."""
        url = reverse("core:terms-of-service")
        self.assertEqual(resolve(url).func.view_class, TermsOfServiceView)
