"""
Тестове за компонента за смяна на езика
"""

from django.test import TestCase, Client
from django.utils.translation import activate, deactivate
from django.urls import reverse
from core.models import City, Location, PropertyType, Feature, TeamMember, Property


class LanguageSwitcherTests(TestCase):
    """Тестове за функционалността на смяната на езика"""

    def setUp(self):
        """Настройка на тестови данни"""
        self.client = Client()

        # Създаване на тестови обекти
        self.city = City.objects.create(name="София")
        self.location = Location.objects.create(name="Център", city=self.city)
        self.property_type = PropertyType.objects.create(
            name="Апартамент", slug="apartment"
        )

    def test_language_prefixed_urls_work(self):
        """Тества дали URL-ите с езикови префикси работят правилно"""

        # Тест за български (по подразбиране)
        response = self.client.get("/bg/")
        self.assertEqual(response.status_code, 200)

        # Тест за английски
        response = self.client.get("/en/")
        self.assertEqual(response.status_code, 200)

        # Тест за гръцки
        response = self.client.get("/el/")
        self.assertEqual(response.status_code, 200)

        # Тест за руски
        response = self.client.get("/ru/")
        self.assertEqual(response.status_code, 200)

    def test_html_lang_attribute_is_dynamic(self):
        """Тества дали HTML lang атрибутът се променя според езика"""

        # Тест за български - проверява само наличието на lang атрибут
        response = self.client.get("/bg/")
        self.assertContains(response, "lang=")

        # Тест за английски
        response = self.client.get("/en/")
        self.assertContains(response, "lang=")

        # Тест за гръцки
        response = self.client.get("/el/")
        self.assertContains(response, "lang=")

        # Тест за руски
        response = self.client.get("/ru/")
        self.assertContains(response, "lang=")

    def test_language_switcher_component_exists_in_header(self):
        """Тества дали компонентът за смяна на езика присъства в хедъра"""

        response = self.client.get("/bg/")
        self.assertEqual(response.status_code, 200)

        # Проверява дали Alpine.js компонентът е в темплейта
        self.assertContains(response, 'x-data="languageSwitcher"')

        # Проверява дали има aria-label със съдържание за избор на език
        self.assertContains(response, "aria-label=")

        # Проверява дали мобилната секция за език присъства
        self.assertContains(response, "Language Switcher Section (Mobile)")

    def test_language_switcher_javascript_component_loaded(self):
        """Тества дали JavaScript компонентът се зарежда"""

        response = self.client.get("/bg/")
        self.assertEqual(response.status_code, 200)

        # Проверява дали има референция към Alpine.js компонента
        self.assertContains(response, "languageSwitcher")

    def test_property_list_works_with_different_languages(self):
        """Тества дали списъкът с имоти работи с различни езици"""

        # Създаване на тестов имот
        broker = TeamMember.objects.create(
            name="Тест Брокер",
            phone="123456789",
            email="<EMAIL>",
            title="Консултант",
        )

        property_obj = Property.objects.create(
            title="Тестов Имот",
            slug="testov-imot",
            location=self.location,
            property_type=self.property_type,
            price=200000,
            area=100,
            description="Описание на тестовия имот",
            assigned_broker=broker,
            is_published=True,
        )

        # Тест за различни езици
        for lang_code in ["bg", "en", "el", "ru"]:
            response = self.client.get(f"/{lang_code}/properties/")
            self.assertEqual(response.status_code, 200)
            # Проверява дали страницата се зарежда без грешки
            self.assertContains(response, property_obj.title)

    def tearDown(self):
        """Почистване след тестове"""
        deactivate()
