"""
Тестове за функционалността за интернационализация (i18n)
"""

from django.test import TestCase, Client
from django.urls import reverse
from django.utils import translation
from django.conf import settings
from core.models import Property, City, Location, PropertyType, TeamMember, Feature


class InternationalizationTests(TestCase):
    """Тестове за многоезичната функционалност"""

    def setUp(self):
        """Настройка на тестови данни"""
        self.client = Client()

        # Създаване на основни обекти
        self.city = City.objects.create(name="София")
        self.location = Location.objects.create(name="Център", city=self.city)
        self.property_type = PropertyType.objects.create(name="Апартамент")
        self.broker = TeamMember.objects.create(
            name="Тест Брокер",
            email="<EMAIL>",
            phone="123456789",
            title="Консултант",
        )
        self.feature = Feature.objects.create(name="Гараж")

        # Създаване на имот за тестване
        self.property = Property.objects.create(
            title="Тестов Имот",
            slug="testov-imot",
            location=self.location,
            property_type=self.property_type,
            price=200000,
            area=100,
            description="Описание на тестовия имот",
            assigned_broker=self.broker,
            is_published=True,
        )
        self.property.features.add(self.feature)

    def test_supported_languages_configuration(self):
        """Тества дали поддържаните езици са правилно конфигурирани"""
        expected_languages = ["bg", "en", "el", "ru"]
        configured_languages = [lang[0] for lang in settings.LANGUAGES]

        for lang in expected_languages:
            self.assertIn(lang, configured_languages)

        # Проверява дали българският е език по подразбиране
        self.assertEqual(settings.LANGUAGE_CODE, "bg")

    def test_language_prefixed_urls(self):
        """Тества дали URL-ите имат езикови префикси"""
        # Тест за българския префикс (по подразбиране)
        response = self.client.get("/bg/")
        self.assertEqual(response.status_code, 200)

        # Тест за английски префикс
        response = self.client.get("/en/")
        self.assertEqual(response.status_code, 200)

    def test_root_url_redirects_to_default_language(self):
        """Тества дали основният URL пренасочва към езика по подразбиране"""
        response = self.client.get("/")
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, "/bg/")

    def test_model_translation_fields_exist(self):
        """Тества дали полетата за превод са създадени в моделите"""
        # Проверява дали са създадени полета за всички езици
        languages = ["bg", "en", "el", "ru"]

        # Тест за Property модела
        property_fields = [f.name for f in Property._meta.get_fields()]
        for lang in languages:
            self.assertIn(f"title_{lang}", property_fields)
            self.assertIn(f"description_{lang}", property_fields)

        # Тест за City модела
        city_fields = [f.name for f in City._meta.get_fields()]
        for lang in languages:
            self.assertIn(f"name_{lang}", city_fields)

        # Тест за PropertyType модела
        propertytype_fields = [f.name for f in PropertyType._meta.get_fields()]
        for lang in languages:
            self.assertIn(f"name_{lang}", propertytype_fields)

    def test_language_switching_in_views(self):
        """Тества дали смяната на езика работи в изгледите"""
        # Активираме английски език
        with translation.override("en"):
            response = self.client.get("/en/")
            self.assertEqual(response.status_code, 200)
            # Проверяваме дали правилният език е активен
            self.assertEqual(translation.get_language(), "en")

        # Активираме български език
        with translation.override("bg"):
            response = self.client.get("/bg/")
            self.assertEqual(response.status_code, 200)
            self.assertEqual(translation.get_language(), "bg")

    def test_property_list_works_with_language_prefix(self):
        """Тества дали списъкът с имоти работи с езикови префикси"""
        # Български
        response = self.client.get("/bg/properties/")
        self.assertEqual(response.status_code, 200)

        # Английски
        response = self.client.get("/en/properties/")
        self.assertEqual(response.status_code, 200)

    def test_property_detail_works_with_language_prefix(self):
        """Тества дали детайлната страница работи с езикови префикси"""
        # Български
        response = self.client.get(f"/bg/properties/{self.property.slug}/")
        self.assertEqual(response.status_code, 200)

        # Английски
        response = self.client.get(f"/en/properties/{self.property.slug}/")
        self.assertEqual(response.status_code, 200)

    def test_admin_urls_not_affected_by_i18n(self):
        """Тества дали админ URL-ите не са засегнати от i18n конфигурацията"""
        response = self.client.get("/admin/")
        # Трябва да пренасочи към admin login, а не да даде 404
        self.assertIn(response.status_code, [200, 302])


class ModelTranslationTests(TestCase):
    """Тестове за преводите на моделите"""

    def setUp(self):
        """Настройка на тестови данни"""
        self.city = City.objects.create(name="София")
        self.location = Location.objects.create(name="Център", city=self.city)
        self.property_type = PropertyType.objects.create(name="Апартамент")
        self.feature = Feature.objects.create(name="Гараж")

    def test_city_translation_fields(self):
        """Тества полетата за превод на City модела"""
        # Задаваме стойности за различните езици
        self.city.name_bg = "София"
        self.city.name_en = "Sofia"
        self.city.name_el = "Σόφια"
        self.city.name_ru = "София"
        self.city.save()

        # Проверяваме дали стойностите са запазени
        city = City.objects.get(pk=self.city.pk)
        self.assertEqual(city.name_bg, "София")
        self.assertEqual(city.name_en, "Sofia")
        self.assertEqual(city.name_el, "Σόφια")
        self.assertEqual(city.name_ru, "София")

    def test_property_type_translation_fields(self):
        """Тества полетата за превод на PropertyType модела"""
        self.property_type.name_bg = "Апартамент"
        self.property_type.name_en = "Apartment"
        self.property_type.name_el = "Διαμέρισμα"
        self.property_type.name_ru = "Квартира"
        self.property_type.save()

        prop_type = PropertyType.objects.get(pk=self.property_type.pk)
        self.assertEqual(prop_type.name_bg, "Апартамент")
        self.assertEqual(prop_type.name_en, "Apartment")
        self.assertEqual(prop_type.name_el, "Διαμέρισμα")
        self.assertEqual(prop_type.name_ru, "Квартира")

    def test_feature_translation_fields(self):
        """Тества полетата за превод на Feature модела"""
        self.feature.name_bg = "Гараж"
        self.feature.name_en = "Garage"
        self.feature.name_el = "Γκαράζ"
        self.feature.name_ru = "Гараж"
        self.feature.save()

        feature = Feature.objects.get(pk=self.feature.pk)
        self.assertEqual(feature.name_bg, "Гараж")
        self.assertEqual(feature.name_en, "Garage")
        self.assertEqual(feature.name_el, "Γκαράζ")
        self.assertEqual(feature.name_ru, "Гараж")
