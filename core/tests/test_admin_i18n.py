"""
Тестове за многоезичния административен интерфейс
"""

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from core.models import City, PropertyType, Feature, Property, Location, TeamMember


class AdminMultiLanguageTests(TestCase):
    """Тестове за многоезичния админ интерфейс"""

    def setUp(self):
        """Настройка на тестови данни"""
        self.client = Client()

        # Създаване на супер потребител
        self.admin_user = User.objects.create_superuser(
            username="testadmin", email="<EMAIL>", password="testpass123"
        )

        # Влизане в админ панела
        self.client.login(username="testadmin", password="testpass123")

        # Създаване на тестови обекти
        self.city = City.objects.create(name="София")
        self.location = Location.objects.create(name="Център", city=self.city)
        self.property_type = PropertyType.objects.create(name="Апартамент")
        self.feature = Feature.objects.create(name="Гараж")
        self.broker = TeamMember.objects.create(
            name="Тест Брокер",
            email="<EMAIL>",
            phone="123456789",
            title="Консултант",
        )

    def test_admin_login_works(self):
        """Тества дали влизането в админ панела работи"""
        response = self.client.get("/admin/")
        self.assertEqual(response.status_code, 200)

    def test_city_admin_has_translation_fields(self):
        """Тества дали City админ показва полетата за превод"""
        response = self.client.get(f"/admin/core/city/{self.city.pk}/change/")
        self.assertEqual(response.status_code, 200)

        # Проверява дали полетата за превод се показват
        self.assertContains(response, "name_bg")
        self.assertContains(response, "name_en")
        self.assertContains(response, "name_el")
        self.assertContains(response, "name_ru")

    def test_property_type_admin_has_translation_fields(self):
        """Тества дали PropertyType админ показва полетата за превод"""
        response = self.client.get(
            f"/admin/core/propertytype/{self.property_type.pk}/change/"
        )
        self.assertEqual(response.status_code, 200)

        # Проверява дали полетата за превод се показват
        self.assertContains(response, "name_bg")
        self.assertContains(response, "name_en")
        self.assertContains(response, "name_el")
        self.assertContains(response, "name_ru")

    def test_feature_admin_has_translation_fields(self):
        """Тества дали Feature админ показва полетата за превод"""
        response = self.client.get(f"/admin/core/feature/{self.feature.pk}/change/")
        self.assertEqual(response.status_code, 200)

        # Проверява дали полетата за превод се показват
        self.assertContains(response, "name_bg")
        self.assertContains(response, "name_en")
        self.assertContains(response, "name_el")
        self.assertContains(response, "name_ru")

    def test_property_admin_has_translation_fields(self):
        """Тества дали Property админ има многоезична поддръжка"""
        property_obj = Property.objects.create(
            title="Тестов Имот",
            slug="testov-imot",
            location=self.location,
            property_type=self.property_type,
            price=200000,
            area=100,
            description="Описание на тестовия имот",
            assigned_broker=self.broker,
            is_published=True,
        )

        response = self.client.get(f"/admin/core/property/{property_obj.pk}/change/")
        self.assertEqual(response.status_code, 200)

        # Проверява дали има полета за заглавие и описание (ключовите полета за превод)
        self.assertContains(response, 'name="title"')
        self.assertContains(response, 'name="description"')

        # Проверява дали страницата се зарежда без грешки
        self.assertNotContains(response, "Error")

    def test_admin_list_views_work(self):
        """Тества дали списъците в админ панела работят правилно"""
        models_to_test = [
            "city",
            "location",
            "propertytype",
            "feature",
            "property",
            "teammember",
        ]

        for model in models_to_test:
            url = f"/admin/core/{model}/"
            response = self.client.get(url)
            self.assertEqual(response.status_code, 200, f"Failed for model: {model}")

    def test_modeltranslation_integration(self):
        """Тества дали modeltranslation интеграцията работи правилно"""
        # Задаваме стойности на различните езици за град
        self.city.name_bg = "София"
        self.city.name_en = "Sofia"
        self.city.name_el = "Σόφια"
        self.city.name_ru = "София"
        self.city.save()

        # Презареждаме от базата данни
        city_reloaded = City.objects.get(pk=self.city.pk)
        self.assertEqual(city_reloaded.name_bg, "София")
        self.assertEqual(city_reloaded.name_en, "Sofia")
        self.assertEqual(city_reloaded.name_el, "Σόφια")
        self.assertEqual(city_reloaded.name_ru, "София")

        # Проверяваме дали админ формата може да работи с тези стойности
        response = self.client.get(f"/admin/core/city/{self.city.pk}/change/")
        self.assertEqual(response.status_code, 200)

    def test_admin_add_views_work(self):
        """Тества дали формите за добавяне в админ панела работят"""
        models_to_test = ["city", "location", "propertytype", "feature", "teammember"]

        for model in models_to_test:
            url = f"/admin/core/{model}/add/"
            response = self.client.get(url)
            self.assertEqual(
                response.status_code, 200, f"Failed add view for model: {model}"
            )
