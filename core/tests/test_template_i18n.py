"""
Тестове за използването на {% trans %} в шаблоните
"""

from django.test import TestCase, Client
from django.utils.translation import activate, deactivate
from django.urls import reverse
from core.models import City, Location, PropertyType, Feature, TeamMember, Property


class TemplateTranslationTests(TestCase):
    """Тестове за правилното използване на {% trans %} в шаблоните"""

    def setUp(self):
        """Настройка на тестови данни"""
        self.client = Client()

        # Създаване на тестови обекти
        self.city = City.objects.create(name="София")
        self.location = Location.objects.create(name="Център", city=self.city)
        self.property_type = PropertyType.objects.create(
            name="Апартамент", slug="apartment"
        )
        self.feature = Feature.objects.create(name="Гараж")
        self.broker = TeamMember.objects.create(
            name="Тест Брокер",
            email="<EMAIL>",
            phone="123456789",
            title="Консултант",
        )

    def test_home_page_translations(self):
        """Тества дали началната страница съдържа преводими текстове"""
        response = self.client.get(reverse("core:home"))
        self.assertEqual(response.status_code, 200)

        # Проверява дали шаблонът се зарежда без грешки
        self.assertContains(response, "Локация")  # Един от ключовите преводи
        self.assertContains(response, "Тип имот")  # Друг ключов превод
        self.assertContains(response, "Търси имоти")  # Третия ключов превод

    def test_header_navigation_translations(self):
        """Тества дали навигацията в хедъра съдържа преводими текстове"""
        response = self.client.get(reverse("core:home"))
        self.assertEqual(response.status_code, 200)

        # Проверява дали навигационните връзки са преводими
        self.assertContains(response, "Начало")
        self.assertContains(response, "Имоти")
        self.assertContains(response, "За нас")
        self.assertContains(response, "Контакти")

    def test_search_form_labels_are_translated(self):
        """Тества дали етикетите във формата за търсене са преводими"""
        response = self.client.get(reverse("core:home"))
        self.assertEqual(response.status_code, 200)

        # Проверява специфично ключовите преводи от .po файловете
        self.assertContains(response, "Локация")
        self.assertContains(response, "Тип имот")
        self.assertContains(response, "Търси имоти")

    def test_template_loads_with_i18n_tags(self):
        """Тества дали шаблоните се зареждат правилно с i18n тагове"""
        # Тест за началната страница
        response = self.client.get(reverse("core:home"))
        self.assertEqual(response.status_code, 200)

        # Тест за страницата с имоти
        response = self.client.get(reverse("core:property-list"))
        self.assertEqual(response.status_code, 200)

        # Тест за другите статични страници
        static_pages = ["core:about", "core:contact"]
        for page in static_pages:
            response = self.client.get(reverse(page))
            self.assertEqual(response.status_code, 200)

    def test_mobile_menu_translations(self):
        """Тества дали мобилното меню съдържа преводими текстове"""
        response = self.client.get(reverse("core:home"))
        self.assertEqual(response.status_code, 200)

        # Проверява дали мобилното меню съдържа преводи
        self.assertContains(response, "Меню")
        self.assertContains(response, "Тема на интерфейса")

    def test_property_list_with_translations(self):
        """Тества дали страницата с имоти работи с преводи"""
        # Създаваме тестов имот
        property_obj = Property.objects.create(
            title="Тестов Имот",
            slug="testov-imot",
            location=self.location,
            property_type=self.property_type,
            price=200000,
            area=100,
            description="Описание на тестовия имот",
            assigned_broker=self.broker,
            is_published=True,
        )

        response = self.client.get(reverse("core:property-list"))
        self.assertEqual(response.status_code, 200)

        # Проверява дали страницата се зарежда без грешки
        self.assertNotContains(response, "Error")

    def test_language_context_switching(self):
        """Тества дали преводите могат да се сменят по език"""
        # Тест с български език (по подразбиране)
        response = self.client.get("/bg/")
        self.assertEqual(response.status_code, 200)

        # Тест с английски език
        response = self.client.get("/en/")
        self.assertEqual(response.status_code, 200)

        # Проверяваме дали URL-ите с езикови префикси работят
        self.assertTrue(True)  # Просто потвърждаваме, че заявките са успешни

    def test_base_template_meta_translations(self):
        """Тества дали мета информацията в base template се превежда"""
        response = self.client.get(reverse("core:home"))
        self.assertEqual(response.status_code, 200)

        # Проверява дали title тага се обработва правилно
        self.assertIn("<title>", response.content.decode())

        # Проверява дали meta description тага се обработва правилно
        self.assertIn('name="description"', response.content.decode())

    def test_property_detail_with_translations(self):
        """Тества дали детайлната страница на имот работи с преводи"""
        # Създаваме тестов имот
        property_obj = Property.objects.create(
            title="Тестов Имот",
            slug="testov-imot",
            location=self.location,
            property_type=self.property_type,
            price=200000,
            area=100,
            description="Описание на тестовия имот",
            assigned_broker=self.broker,
            is_published=True,
        )

        response = self.client.get(
            reverse("core:property-detail", kwargs={"slug": "testov-imot"})
        )
        self.assertEqual(response.status_code, 200)

        # Проверява дали страницата се зарежда без грешки
        self.assertNotContains(response, "Error")
        self.assertContains(response, "Тестов Имот")
