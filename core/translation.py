"""
Конфигурация за многоезичност с django-modeltranslation
"""

from modeltranslation.translator import register, TranslationOptions
from .models import (
    Property,
    PropertyType,
    Feature,
    Testimonial,
    City,
    Location,
    TeamMember,
)


@register(City)
class CityTranslationOptions(TranslationOptions):
    fields = ("name",)


@register(Location)
class LocationTranslationOptions(TranslationOptions):
    fields = ("name",)


@register(PropertyType)
class PropertyTypeTranslationOptions(TranslationOptions):
    fields = (
        "name",
    )  # Временно премахваме slug от преводи заради unique constraint проблеми


@register(Feature)
class FeatureTranslationOptions(TranslationOptions):
    fields = ("name",)


@register(Property)
class PropertyTranslationOptions(TranslationOptions):
    fields = (
        "title",
        "description",
    )  # Временно премахваме slug от преводи заради unique constraint проблеми


@register(Testimonial)
class TestimonialTranslationOptions(TranslationOptions):
    fields = ("client_name", "quote")


@register(TeamMember)
class TeamMemberTranslationOptions(TranslationOptions):
    fields = ("name", "title")
