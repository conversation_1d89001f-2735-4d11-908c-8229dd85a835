{% load static l10n %}

<div class="w-full max-w-sm bg-surface rounded-radius shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 group">
    <!-- Property Image -->
    <div class="relative overflow-hidden">
        {% if property.main_image %}
            <img src="{{ property.main_image.image.url }}"
                 alt="{{ property.title }}"
                 class="w-full h-48 sm:h-56 object-cover group-hover:scale-105 transition-transform duration-300">
        {% else %}
            <div class="w-full h-48 sm:h-56 bg-surface-variant flex items-center justify-center">
                <svg class="w-16 h-16 text-on-surface-variant"
                     fill="none"
                     stroke="currentColor"
                     viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z">
                    </path>
                </svg>
            </div>
        {% endif %}

        <!-- Featured Badge -->
        {% if property.is_featured %}
            <div class="absolute top-4 left-4 bg-primary text-on-primary px-3 py-1 rounded-radius text-sm font-medium">
                Топ оферта
            </div>
        {% endif %}

        <!-- Price Badge -->
        <div class="absolute top-4 right-4 bg-surface/90 text-on-surface px-3 py-1 rounded-radius font-bold">
            {% comment %} {{ property.price_formatted }} {% endcomment %}
            {{ property.price|localize }}
        </div>
    </div>

    <!-- Property Info -->
    <div class="p-6">
        <!-- Property Type and Location -->
        <div class="flex items-center text-sm text-on-surface-variant mb-2">
            <span>{{ property.property_type.name }}</span>
            <span class="mx-2">•</span>
            <span>{{ property.location.name }}, {{ property.location.city.name }}</span>
        </div>

        <!-- Property Title -->
        <h3 class="font-title text-xl font-semibold text-on-surface mb-3 group-hover:text-primary transition-colors duration-200">
            <a href="{{ property.get_absolute_url }}" class="hover:underline">{{ property.title }}</a>
        </h3>

        <!-- Property Features -->
        <div class="flex items-center space-x-4 text-sm text-on-surface-variant mb-4">
            <!-- Area -->
            <div class="flex items-center">
                <svg class="w-4 h-4 mr-1"
                     fill="none"
                     stroke="currentColor"
                     viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4">
                    </path>
                </svg>
                <span>{{ property.area }} кв.м</span>
            </div>

            <!-- Bedrooms (if any) -->
            {% if property.bedrooms > 0 %}
                <div class="flex items-center">
                    <svg class="w-4 h-4 mr-1"
                         fill="none"
                         stroke="currentColor"
                         viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z">
                        </path>
                    </svg>
                    <span>{{ property.bedrooms }} спални</span>
                </div>
            {% endif %}
        </div>

        <!-- Property Features Tags -->
        {% if property.features.all %}
            <div class="flex flex-wrap gap-2 mb-4">
                {% for feature in property.features.all|slice:":3" %}
                    <span class="bg-surface-variant text-on-surface-variant px-2 py-1 rounded-radius text-xs">{{ feature.name }}</span>
                {% endfor %}
                {% if property.features.count > 3 %}
                    <span class="bg-surface-variant text-on-surface-variant px-2 py-1 rounded-radius text-xs">
                        +{{ property.features.count|add:"-3" }} още
                    </span>
                {% endif %}
            </div>
        {% endif %}

        <!-- Broker Info -->
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                {% if property.assigned_broker.photo %}
                    <img src="{{ property.assigned_broker.photo.url }}"
                         alt="{{ property.assigned_broker.name }}"
                         class="w-8 h-8 rounded-full object-cover mr-2">
                {% else %}
                    <div class="w-8 h-8 bg-surface-variant rounded-full flex items-center justify-center mr-2">
                        <span class="text-xs font-medium text-on-surface-variant">{{ property.assigned_broker.name|first }}</span>
                    </div>
                {% endif %}
                <div>
                    <p class="text-sm font-medium text-on-surface">{{ property.assigned_broker.name }}</p>
                    <p class="text-xs text-on-surface-variant">{{ property.assigned_broker.title }}</p>
                </div>
            </div>

            <!-- View Details Link -->
            <a href="{{ property.get_absolute_url }}"
               class="text-primary hover:text-primary/80 font-medium text-sm flex items-center">
                Детайли
                <svg class="w-4 h-4 ml-1"
                     fill="none"
                     stroke="currentColor"
                     viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </a>
        </div>
    </div>
</div>
