{% extends 'core/base.html' %}
{% load static i18n %}

{% block title %}
    {% trans "Имоти Bulgaria - Намерете вашия мечтан дом" %}
{% endblock title %}

{% block content %}
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-primary/10 to-secondary/5 py-20 lg:py-32">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="font-title text-4xl md:text-5xl lg:text-6xl font-bold text-on-surface mb-6">
                    {% trans "Открийте Вашия" %} <span class="text-primary">{% trans "мечтан дом" %}</span>
                </h1>
                <p class="text-xl text-on-surface-variant mb-10 max-w-3xl mx-auto">
                    {% trans "Професионални услуги за недвижими имоти в България. Помагаме ви да намерите перфектния дом или да продадете вашия имот по най-добрата цена." %}
                </p>

                <!-- Simple Search Bar -->
                <div class="max-w-4xl mx-auto">
                    <form action="{% url 'core:property-list' %}"
                          method="get"
                          class="bg-surface rounded-radius shadow-lg p-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="location" class="block text-sm font-medium text-on-surface mb-2">{% trans "Локация" %}</label>
                            <select id="location"
                                    name="location"
                                    class="w-full px-4 py-3 rounded-radius border border-outline bg-surface text-on-surface focus:outline-none focus:ring-2 focus:ring-primary">
                                <option value="">{% trans "Избери локация" %}</option>
                                {% for location in locations %}<option value="{{ location.id }}">{{ location.name }}</option>{% endfor %}
                            </select>
                        </div>
                        <div>
                            <label for="property_type"
                                   class="block text-sm font-medium text-on-surface mb-2">
                                {% trans "Тип имот" %}
                            </label>
                            <select id="property_type"
                                    name="property_type"
                                    class="w-full px-4 py-3 rounded-radius border border-outline bg-surface text-on-surface focus:outline-none focus:ring-2 focus:ring-primary">
                                <option value="">{% trans "Всички типове" %}</option>
                                {% for property_type in property_types %}
                                    <option value="{{ property_type.slug }}">{{ property_type.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="flex items-end">
                            <button type="submit" class="btn btn-primary w-full">
                                <svg class="w-5 h-5 inline mr-2"
                                     fill="none"
                                     stroke="currentColor"
                                     viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z">
                                    </path>
                                </svg>
                                {% trans "Търси имоти" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Properties Section -->
    <section class="py-16 lg:py-24">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="font-title text-3xl lg:text-4xl font-bold text-on-surface mb-4">Препоръчани имоти</h2>
                <p class="text-lg text-on-surface-variant max-w-2xl mx-auto">
                    Разгледайте нашата селекция от най-добрите оферти за недвижими имоти
                </p>
            </div>

            {% if featured_properties %}
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {% for property in featured_properties %}
                        {% include 'partials/_property_card.html' with property=property %}
                    {% endfor %}
                </div>

                <div class="text-center mt-12">
                    <a href="{% url 'core:property-list' %}" class="btn btn-lg btn-primary">Виж всички имоти</a>
                </div>
            {% else %}
                <div class="text-center py-12">
                    <p class="text-on-surface-variant">Все още няма препоръчани имоти.</p>
                    <a href="{% url 'core:property-list' %}"
                       class="text-primary hover:underline">Разгледайте всички имоти</a>
                </div>
            {% endif %}
        </div>
    </section>

    <!-- Advantages Section -->
    <section class="py-16 lg:py-24 bg-surface-variant">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="font-title text-3xl lg:text-4xl font-bold text-on-surface mb-4">Защо да изберете нас?</h2>
                <p class="text-lg text-on-surface-variant max-w-2xl mx-auto">Професионализъм, опит и грижа за нашите клиенти</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="text-center group">
                    <div class="w-16 h-16 bg-primary rounded-radius mx-auto mb-6 flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <svg class="w-8 h-8 text-on-primary"
                             fill="none"
                             stroke="currentColor"
                             viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z">
                            </path>
                        </svg>
                    </div>
                    <h3 class="font-title text-xl font-semibold text-on-surface mb-4">Доказан опит</h3>
                    <p class="text-on-surface-variant">Над 10 години опит в сферата на недвижимите имоти и стотици доволни клиенти.</p>
                </div>

                <div class="text-center group">
                    <div class="w-16 h-16 bg-primary rounded-radius mx-auto mb-6 flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <svg class="w-8 h-8 text-on-primary"
                             fill="none"
                             stroke="currentColor"
                             viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                            </path>
                        </svg>
                    </div>
                    <h3 class="font-title text-xl font-semibold text-on-surface mb-4">Най-добри цени</h3>
                    <p class="text-on-surface-variant">Конкурентни цени и прозрачни условия без скрити такси и допълнителни разходи.</p>
                </div>

                <div class="text-center group">
                    <div class="w-16 h-16 bg-primary rounded-radius mx-auto mb-6 flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <svg class="w-8 h-8 text-on-primary"
                             fill="none"
                             stroke="currentColor"
                             viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z">
                            </path>
                        </svg>
                    </div>
                    <h3 class="font-title text-xl font-semibold text-on-surface mb-4">24/7 поддръжка</h3>
                    <p class="text-on-surface-variant">Винаги сме на разположение за въпроси и консултации във всеки етап от процеса.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    {% if testimonials %}
        <section class="py-16 lg:py-24" x-data="testimonialCarousel()">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="font-title text-3xl lg:text-4xl font-bold text-on-surface mb-4">Отзиви от клиенти</h2>
                    <p class="text-lg text-on-surface-variant max-w-2xl mx-auto">Вижте какво споделят нашите клиенти за работата ни</p>
                </div>

                <div class="relative">
                    <div class="max-w-4xl mx-auto">
                        {% for testimonial in testimonials %}
                            <div x-show="current === {{ forloop.counter0 }}"
                                 x-transition:enter="transition ease-out duration-300"
                                 x-transition:enter-start="opacity-0 transform translate-x-8"
                                 x-transition:enter-end="opacity-100 transform translate-x-0"
                                 x-transition:leave="transition ease-in duration-200"
                                 x-transition:leave-start="opacity-100 transform translate-x-0"
                                 x-transition:leave-end="opacity-0 transform -translate-x-8"
                                 class="text-center">
                                <blockquote class="text-xl lg:text-2xl text-on-surface mb-8 font-medium leading-relaxed">
                                    "{{ testimonial.quote }}"
                                </blockquote>
                                <cite class="text-primary font-semibold">{{ testimonial.client_name }}</cite>
                            </div>
                        {% endfor %}
                    </div>

                    <!-- Navigation -->
                    <div class="flex justify-center mt-8 space-x-2">
                        {% for testimonial in testimonials %}
                            <button @click="current = {{ forloop.counter0 }}"
                                    :class="current === {{ forloop.counter0 }} ? 'bg-primary' : 'bg-outline'"
                                    class="w-3 h-3 rounded-full transition-colors duration-200"></button>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </section>
    {% endif %}

    <!-- CTA Section -->
    <section class="py-16 lg:py-24 bg-primary">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="font-title text-3xl lg:text-4xl font-bold text-on-primary mb-6">Готови да намерите мечтания си дом?</h2>
            <p class="text-xl text-on-primary/90 mb-8 max-w-2xl mx-auto">
                Свържете се с нас днес и ще ви помогнем да намерите идеалния имот за вас
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{% url 'core:property-list' %}"
                   class="inline-flex items-center justify-center bg-on-primary text-primary px-8 py-3 rounded-radius font-medium hover:bg-on-primary/90 transition-colors duration-200">
                    Търси имоти
                </a>
                <a href="{% url 'core:contact' %}"
                   class="inline-flex items-center justify-center border-2 border-on-primary text-on-primary px-8 py-3 rounded-radius font-medium hover:bg-on-primary/10 transition-colors duration-200">
                    Свържи се с нас
                </a>
            </div>
        </div>
    </section>

    <script>
// Alpine.js компонент за карусела с отзиви
document.addEventListener('alpine:init', () => {
    Alpine.data('testimonialCarousel', () => ({
        current: 0,
        total: {{ testimonials|length|default:0 }},
        
        init() {
            if (this.total > 1) {
                setInterval(() => {
                    this.current = (this.current + 1) % this.total
                }, 5000) // Сменя отзива на всеки 5 секунди
            }
        }
    }))
})
    </script>
{% endblock content %}
