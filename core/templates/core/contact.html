{% extends 'core/base.html' %}
{% load static %}

{% block title %}
    Контакти - Имоти Bulgaria
{% endblock title %}

{% block content %}
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-primary/10 to-secondary/5 py-20 lg:py-32">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="font-title text-4xl md:text-5xl lg:text-6xl font-bold text-on-surface mb-6">Свържете се с нас</h1>
            <p class="text-xl text-on-surface-variant max-w-3xl mx-auto">
                Готови сме да отговорим на всички ваши въпроси и да ви помогнем с вашите нужди от недвижими имоти
            </p>
        </div>
    </section>

    <!-- Contact Info & Form Section -->
    <section class="py-16 lg:py-24">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="lg:grid lg:grid-cols-3 lg:gap-16">
                <!-- Contact Information -->
                <div class="lg:col-span-1">
                    <h2 class="font-title text-3xl font-bold text-on-surface mb-8">Контактна информация</h2>

                    <!-- Office Address -->
                    <div class="mb-8">
                        <h3 class="font-semibold text-on-surface mb-4 flex items-center">
                            <svg class="w-5 h-5 text-primary mr-2"
                                 fill="none"
                                 stroke="currentColor"
                                 viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                </path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            Адрес
                        </h3>
                        <div class="text-on-surface-variant ml-7">
                            <p>бул. Витоша 15</p>
                            <p>София 1000, България</p>
                        </div>
                    </div>

                    <!-- Phone Numbers -->
                    <div class="mb-8">
                        <h3 class="font-semibold text-on-surface mb-4 flex items-center">
                            <svg class="w-5 h-5 text-primary mr-2"
                                 fill="none"
                                 stroke="currentColor"
                                 viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z">
                                </path>
                            </svg>
                            Телефони
                        </h3>
                        <div class="text-on-surface-variant ml-7 space-y-2">
                            <div>
                                <a href="tel:+359888123456"
                                   class="hover:text-primary transition-colors duration-200">+359 888 123 456</a>
                                <span class="text-sm">(Главен офис)</span>
                            </div>
                            <div>
                                <a href="tel:+359888123457"
                                   class="hover:text-primary transition-colors duration-200">+359 888 123 457</a>
                                <span class="text-sm">(Мобилен)</span>
                            </div>
                        </div>
                    </div>

                    <!-- Email -->
                    <div class="mb-8">
                        <h3 class="font-semibold text-on-surface mb-4 flex items-center">
                            <svg class="w-5 h-5 text-primary mr-2"
                                 fill="none"
                                 stroke="currentColor"
                                 viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                                </path>
                            </svg>
                            Имейл
                        </h3>
                        <div class="text-on-surface-variant ml-7">
                            <a href="mailto:<EMAIL>"
                               class="hover:text-primary transition-colors duration-200"><EMAIL></a>
                        </div>
                    </div>

                    <!-- Working Hours -->
                    <div class="mb-8">
                        <h3 class="font-semibold text-on-surface mb-4 flex items-center">
                            <svg class="w-5 h-5 text-primary mr-2"
                                 fill="none"
                                 stroke="currentColor"
                                 viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z">
                                </path>
                            </svg>
                            Работно време
                        </h3>
                        <div class="text-on-surface-variant ml-7 space-y-1">
                            <p>Понеделник - Петък: 09:00 - 18:00</p>
                            <p>Събота: 10:00 - 16:00</p>
                            <p>Неделя: Почивен ден</p>
                            <p class="text-sm text-primary">* 24/7 помощ по телефон</p>
                        </div>
                    </div>

                    <!-- Social Media -->
                    <div>
                        <h3 class="font-semibold text-on-surface mb-4">Последвайте ни</h3>
                        <div class="flex space-x-4">
                            <a href="#"
                               class="text-on-surface-variant hover:text-primary transition-colors duration-200">
                                <span class="sr-only">Facebook</span>
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                                </svg>
                            </a>
                            <a href="#"
                               class="text-on-surface-variant hover:text-primary transition-colors duration-200">
                                <span class="sr-only">Instagram</span>
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.596-3.205-1.533l1.714-1.116c.414.51 1.043.824 1.491.824.448 0 1.077-.314 1.491-.824l1.714 1.116c-.757.937-1.908 1.533-3.205 1.533zm7.098 0c-1.297 0-2.448-.596-3.205-1.533l1.714-1.116c.414.51 1.043.824 1.491.824s1.077-.314 1.491-.824l1.714 1.116c-.757.937-1.908 1.533-3.205 1.533z" />
                                </svg>
                            </a>
                            <a href="#"
                               class="text-on-surface-variant hover:text-primary transition-colors duration-200">
                                <span class="sr-only">LinkedIn</span>
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Contact Form -->
                <div class="lg:col-span-2 mt-12 lg:mt-0">
                    <div class="bg-surface border border-outline rounded-radius p-8">
                        <h2 class="font-title text-3xl font-bold text-on-surface mb-6">Изпратете ни съобщение</h2>
                        <p class="text-on-surface-variant mb-8">Попълнете формата по-долу и ще се свържем с вас в най-кратко време</p>

                        <form x-data="generalContactForm()"
                              @submit.prevent="submitForm()"
                              class="space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="contact_name"
                                           class="block text-sm font-medium text-on-surface mb-2">Име *</label>
                                    <input type="text"
                                           id="contact_name"
                                           name="name"
                                           x-model="form.name"
                                           required
                                           class="w-full px-4 py-3 border border-outline rounded-radius bg-surface text-on-surface focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>

                                <div>
                                    <label for="contact_email"
                                           class="block text-sm font-medium text-on-surface mb-2">Имейл *</label>
                                    <input type="email"
                                           id="contact_email"
                                           name="email"
                                           x-model="form.email"
                                           required
                                           class="w-full px-4 py-3 border border-outline rounded-radius bg-surface text-on-surface focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                            </div>

                            <div>
                                <label for="contact_phone"
                                       class="block text-sm font-medium text-on-surface mb-2">Телефон</label>
                                <input type="tel"
                                       id="contact_phone"
                                       name="phone"
                                       x-model="form.phone"
                                       class="w-full px-4 py-3 border border-outline rounded-radius bg-surface text-on-surface focus:outline-none focus:ring-2 focus:ring-primary">
                            </div>

                            <div>
                                <label for="contact_subject"
                                       class="block text-sm font-medium text-on-surface mb-2">Тема</label>
                                <select id="contact_subject"
                                        name="subject"
                                        x-model="form.subject"
                                        class="w-full px-4 py-3 border border-outline rounded-radius bg-surface text-on-surface focus:outline-none focus:ring-2 focus:ring-primary">
                                    <option value="">Изберете тема</option>
                                    <option value="buying">Купуване на имот</option>
                                    <option value="selling">Продажба на имот</option>
                                    <option value="renting">Наемане на имот</option>
                                    <option value="valuation">Оценка на имот</option>
                                    <option value="consultation">Консултация</option>
                                    <option value="other">Друго</option>
                                </select>
                            </div>

                            <div>
                                <label for="contact_message"
                                       class="block text-sm font-medium text-on-surface mb-2">Съобщение *</label>
                                <textarea id="contact_message"
                                          name="message"
                                          x-model="form.message"
                                          rows="6"
                                          required
                                          placeholder="Разкажете ни повече за вашите нужди..."
                                          class="w-full px-4 py-3 border border-outline rounded-radius bg-surface text-on-surface focus:outline-none focus:ring-2 focus:ring-primary"></textarea>
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox"
                                       id="contact_consent"
                                       name="consent"
                                       x-model="form.consent"
                                       required
                                       class="rounded border-outline text-primary focus:ring-primary">
                                <label for="contact_consent" class="ml-2 text-sm text-on-surface">
                                    Съгласен/съгласна съм с обработката на личните ми данни съгласно
                                    <a href="{% url 'core:privacy-policy' %}"
                                       class="text-primary hover:underline">Политиката за поверителност</a>
                                </label>
                            </div>

                            <button type="submit"
                                    :disabled="loading || !form.consent"
                                    :class="(loading || !form.consent) ? 'opacity-50 cursor-not-allowed' : ''"
                                    class="btn btn-primary w-full">
                                <span x-show="!loading">Изпрати съобщение</span>
                                <span x-show="loading" class="flex items-center justify-center">
                                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-on-primary"
                                         fill="none"
                                         viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                        </path>
                                    </svg>
                                    Изпраща...
                                </span>
                            </button>

                            <!-- Success Message -->
                            <div x-show="success"
                                 x-transition
                                 class="p-4 bg-success/20 border border-success/30 rounded-radius">
                                <p class="text-success">Съобщението е изпратено успешно! Ще се свържем с вас в най-кратко време.</p>
                            </div>

                            <!-- Error Message -->
                            <div x-show="error"
                                 x-transition
                                 class="p-4 bg-danger/20 border border-danger/30 rounded-radius">
                                <p class="text-danger" x-text="errorMessage"></p>
                            </div>
                        </form>

                        <script>
                    document.addEventListener('alpine:init', () => {
                        Alpine.data('generalContactForm', () => ({
                            form: {
                                name: '',
                                email: '',
                                phone: '',
                                subject: '',
                                message: '',
                                consent: false
                            },
                            loading: false,
                            success: false,
                            error: false,
                            errorMessage: '',

                            async submitForm() {
                                this.loading = true
                                this.success = false
                                this.error = false

                                try {
                                    const formData = new FormData()
                                    formData.append('name', this.form.name)
                                    formData.append('email', this.form.email)
                                    formData.append('phone', this.form.phone)
                                    
                                    let message = this.form.message
                                    if (this.form.subject) {
                                        message = `Тема: ${this.form.subject}\n\n${message}`
                                    }
                                    formData.append('message', message)

                                    const response = await fetch('{% url "core:contact-inquiry-create" %}', {
                                        method: 'POST',
                                        body: formData,
                                        headers: {
                                            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                                        }
                                    })

                                    const data = await response.json()

                                    if (data.success) {
                                        this.success = true
                                        this.form = {
                                            name: '',
                                            email: '',
                                            phone: '',
                                            subject: '',
                                            message: '',
                                            consent: false
                                        }
                                    } else {
                                        this.error = true
                                        this.errorMessage = data.error || 'Възникна грешка при изпращане на съобщението.'
                                    }
                                } catch (err) {
                                    this.error = true
                                    this.errorMessage = 'Възникна грешка при изпращане на съобщението.'
                                } finally {
                                    this.loading = false
                                }
                            }
                        }))
                    })
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Map Section -->
    <section class="py-16 lg:py-24 bg-surface-variant">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="font-title text-3xl lg:text-4xl font-bold text-on-surface mb-4">Намерете ни</h2>
                <p class="text-lg text-on-surface-variant">Офисът ни се намира в сърцето на София</p>
            </div>

            <!-- Map placeholder -->
            <div class="w-full h-96 bg-surface rounded-radius border border-outline flex items-center justify-center">
                <div class="text-center">
                    <svg class="mx-auto h-16 w-16 text-on-surface-variant mb-4"
                         fill="none"
                         stroke="currentColor"
                         viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                        </path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <h3 class="text-xl font-semibold text-on-surface mb-2">Имоти Bulgaria</h3>
                    <p class="text-on-surface-variant mb-4">
                        бул. Витоша 15
                        <br>
                        София 1000, България
                    </p>
                    <a href="https://maps.google.com/?q=бул.Витоша+15+София"
                       target="_blank"
                       class="inline-flex items-center text-primary hover:text-primary/80 transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2"
                             fill="none"
                             stroke="currentColor"
                             viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14">
                            </path>
                        </svg>
                        Отвори в Google Maps
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- CSRF Token for AJAX requests -->
    {% csrf_token %}
{% endblock content %}
