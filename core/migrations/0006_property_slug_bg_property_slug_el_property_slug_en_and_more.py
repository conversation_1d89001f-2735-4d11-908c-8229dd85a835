# Generated by Django 5.2.4 on 2025-07-23 12:03

import autoslug.fields
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0005_teammember_name_bg_teammember_name_el_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='property',
            name='slug_bg',
            field=autoslug.fields.AutoSlugField(editable=False, max_length=200, null=True, populate_from='title', unique=True, verbose_name='URL адрес'),
        ),
        migrations.AddField(
            model_name='property',
            name='slug_el',
            field=autoslug.fields.AutoSlugField(editable=False, max_length=200, null=True, populate_from='title', unique=True, verbose_name='URL адрес'),
        ),
        migrations.AddField(
            model_name='property',
            name='slug_en',
            field=autoslug.fields.AutoSlugField(editable=False, max_length=200, null=True, populate_from='title', unique=True, verbose_name='URL адрес'),
        ),
        migrations.AddField(
            model_name='property',
            name='slug_ru',
            field=autoslug.fields.AutoSlugField(editable=False, max_length=200, null=True, populate_from='title', unique=True, verbose_name='URL адрес'),
        ),
        migrations.AddField(
            model_name='propertytype',
            name='slug_bg',
            field=autoslug.fields.AutoSlugField(editable=False, null=True, populate_from='name', unique=True, verbose_name='URL адрес'),
        ),
        migrations.AddField(
            model_name='propertytype',
            name='slug_el',
            field=autoslug.fields.AutoSlugField(editable=False, null=True, populate_from='name', unique=True, verbose_name='URL адрес'),
        ),
        migrations.AddField(
            model_name='propertytype',
            name='slug_en',
            field=autoslug.fields.AutoSlugField(editable=False, null=True, populate_from='name', unique=True, verbose_name='URL адрес'),
        ),
        migrations.AddField(
            model_name='propertytype',
            name='slug_ru',
            field=autoslug.fields.AutoSlugField(editable=False, null=True, populate_from='name', unique=True, verbose_name='URL адрес'),
        ),
        migrations.AlterField(
            model_name='property',
            name='slug',
            field=autoslug.fields.AutoSlugField(editable=False, max_length=200, populate_from='title', unique=True, verbose_name='URL адрес'),
        ),
        migrations.AlterField(
            model_name='propertytype',
            name='slug',
            field=autoslug.fields.AutoSlugField(editable=False, populate_from='name', unique=True, verbose_name='URL адрес'),
        ),
    ]
