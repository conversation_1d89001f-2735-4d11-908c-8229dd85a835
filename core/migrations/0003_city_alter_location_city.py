# Generated by Django 5.2.4 on 2025-07-22 06:28

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0002_alter_location_options_remove_location_parent_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='City',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Име на града')),
            ],
            options={
                'verbose_name': 'Град',
                'verbose_name_plural': 'Градове',
                'ordering': ['name'],
            },
        ),
        migrations.AlterField(
            model_name='location',
            name='city',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.city', verbose_name='Град'),
        ),
    ]
