# Generated by Django 5.2.4 on 2025-07-23 11:13

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0004_city_name_bg_city_name_el_city_name_en_city_name_ru_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='teammember',
            name='name_bg',
            field=models.CharField(max_length=100, null=True, verbose_name='Име'),
        ),
        migrations.AddField(
            model_name='teammember',
            name='name_el',
            field=models.CharField(max_length=100, null=True, verbose_name='Име'),
        ),
        migrations.AddField(
            model_name='teammember',
            name='name_en',
            field=models.CharField(max_length=100, null=True, verbose_name='Име'),
        ),
        migrations.AddField(
            model_name='teammember',
            name='name_ru',
            field=models.Char<PERSON>ield(max_length=100, null=True, verbose_name='Име'),
        ),
        migrations.AddField(
            model_name='teammember',
            name='title_bg',
            field=models.CharField(max_length=100, null=True, verbose_name='Длъжност'),
        ),
        migrations.AddField(
            model_name='teammember',
            name='title_el',
            field=models.CharField(max_length=100, null=True, verbose_name='Длъжност'),
        ),
        migrations.AddField(
            model_name='teammember',
            name='title_en',
            field=models.CharField(max_length=100, null=True, verbose_name='Длъжност'),
        ),
        migrations.AddField(
            model_name='teammember',
            name='title_ru',
            field=models.CharField(max_length=100, null=True, verbose_name='Длъжност'),
        ),
    ]
