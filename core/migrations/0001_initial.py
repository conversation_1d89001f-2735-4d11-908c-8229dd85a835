# Generated by Django 5.2.4 on 2025-07-21 14:38

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Feature',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='Име на особеността')),
            ],
            options={
                'verbose_name': 'Особеност',
                'verbose_name_plural': 'Особености',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='PropertyType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=50, verbose_name='Име на типа')),
                ('slug', models.SlugField(unique=True, verbose_name='URL адрес')),
            ],
            options={
                'verbose_name': 'Тип имот',
                'verbose_name_plural': 'Типове имоти',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='TeamMember',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Име')),
                ('photo', models.ImageField(blank=True, null=True, upload_to='team_photos/', verbose_name='Снимка')),
                ('phone', models.CharField(max_length=20, verbose_name='Телефонен номер')),
                ('email', models.EmailField(max_length=254, verbose_name='Имейл адрес')),
                ('title', models.CharField(max_length=100, verbose_name='Длъжност')),
            ],
            options={
                'verbose_name': 'Член на екипа',
                'verbose_name_plural': 'Членове на екипа',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Testimonial',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('client_name', models.CharField(max_length=100, verbose_name='Име на клиента')),
                ('quote', models.TextField(verbose_name='Текст на отзива')),
                ('is_active', models.BooleanField(default=True, verbose_name='Активен отзив')),
            ],
            options={
                'verbose_name': 'Отзив',
                'verbose_name_plural': 'Отзиви',
                'ordering': ['client_name'],
            },
        ),
        migrations.CreateModel(
            name='Location',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Име на локацията')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.location', verbose_name='Родителска локация')),
            ],
            options={
                'verbose_name': 'Локация',
                'verbose_name_plural': 'Локации',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Property',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='Заглавие на обявата')),
                ('slug', models.SlugField(max_length=200, unique=True, verbose_name='URL адрес')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Цена')),
                ('area', models.IntegerField(verbose_name='Площ (кв.м.)')),
                ('bedrooms', models.PositiveIntegerField(default=0, verbose_name='Брой спални')),
                ('description', models.TextField(verbose_name='Пълно описание')),
                ('is_published', models.BooleanField(default=True, verbose_name='Публикувана обява')),
                ('is_featured', models.BooleanField(default=False, verbose_name='Препоръчан имот')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Дата на създаване')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Дата на обновяване')),
                ('features', models.ManyToManyField(blank=True, to='core.feature', verbose_name='Особености')),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.location', verbose_name='Локация')),
                ('property_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.propertytype', verbose_name='Тип имот')),
                ('assigned_broker', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.teammember', verbose_name='Отговорен брокер')),
            ],
            options={
                'verbose_name': 'Имот',
                'verbose_name_plural': 'Имоти',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ContactInquiry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Име')),
                ('email', models.EmailField(max_length=254, verbose_name='Имейл адрес')),
                ('phone', models.CharField(max_length=20, verbose_name='Телефонен номер')),
                ('message', models.TextField(verbose_name='Съобщение')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Дата на получаване')),
                ('is_handled', models.BooleanField(default=False, verbose_name='Обработено запитване')),
                ('property', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.property', verbose_name='Имот (ако е за конкретна обява)')),
            ],
            options={
                'verbose_name': 'Запитване за контакт',
                'verbose_name_plural': 'Запитвания за контакт',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PropertyImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='property_images/', verbose_name='Снимка')),
                ('order', models.PositiveIntegerField(default=1, verbose_name='Поредност')),
                ('property', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='core.property', verbose_name='Имот')),
            ],
            options={
                'verbose_name': 'Снимка на имот',
                'verbose_name_plural': 'Снимки на имоти',
                'ordering': ['order'],
                'unique_together': {('property', 'order')},
            },
        ),
    ]
