# Generated by Django 5.2.4 on 2025-07-22 06:24

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='location',
            options={'ordering': ['city', 'name'], 'verbose_name': 'Локация', 'verbose_name_plural': 'Локации'},
        ),
        migrations.RemoveField(
            model_name='location',
            name='parent',
        ),
        migrations.AddField(
            model_name='location',
            name='city',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.location', verbose_name='Град'),
        ),
    ]
