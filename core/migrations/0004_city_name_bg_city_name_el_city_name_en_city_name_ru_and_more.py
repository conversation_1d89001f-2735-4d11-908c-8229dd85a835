# Generated by Django 5.2.4 on 2025-07-23 09:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0003_city_alter_location_city'),
    ]

    operations = [
        migrations.AddField(
            model_name='city',
            name='name_bg',
            field=models.CharField(max_length=100, null=True, verbose_name='Име на града'),
        ),
        migrations.AddField(
            model_name='city',
            name='name_el',
            field=models.CharField(max_length=100, null=True, verbose_name='Име на града'),
        ),
        migrations.AddField(
            model_name='city',
            name='name_en',
            field=models.CharField(max_length=100, null=True, verbose_name='Име на града'),
        ),
        migrations.AddField(
            model_name='city',
            name='name_ru',
            field=models.CharField(max_length=100, null=True, verbose_name='Име на града'),
        ),
        migrations.AddField(
            model_name='feature',
            name='name_bg',
            field=models.CharField(max_length=50, null=True, verbose_name='Име на особеността'),
        ),
        migrations.AddField(
            model_name='feature',
            name='name_el',
            field=models.CharField(max_length=50, null=True, verbose_name='Име на особеността'),
        ),
        migrations.AddField(
            model_name='feature',
            name='name_en',
            field=models.CharField(max_length=50, null=True, verbose_name='Име на особеността'),
        ),
        migrations.AddField(
            model_name='feature',
            name='name_ru',
            field=models.CharField(max_length=50, null=True, verbose_name='Име на особеността'),
        ),
        migrations.AddField(
            model_name='location',
            name='name_bg',
            field=models.CharField(max_length=100, null=True, verbose_name='Име на локацията'),
        ),
        migrations.AddField(
            model_name='location',
            name='name_el',
            field=models.CharField(max_length=100, null=True, verbose_name='Име на локацията'),
        ),
        migrations.AddField(
            model_name='location',
            name='name_en',
            field=models.CharField(max_length=100, null=True, verbose_name='Име на локацията'),
        ),
        migrations.AddField(
            model_name='location',
            name='name_ru',
            field=models.CharField(max_length=100, null=True, verbose_name='Име на локацията'),
        ),
        migrations.AddField(
            model_name='property',
            name='description_bg',
            field=models.TextField(null=True, verbose_name='Пълно описание'),
        ),
        migrations.AddField(
            model_name='property',
            name='description_el',
            field=models.TextField(null=True, verbose_name='Пълно описание'),
        ),
        migrations.AddField(
            model_name='property',
            name='description_en',
            field=models.TextField(null=True, verbose_name='Пълно описание'),
        ),
        migrations.AddField(
            model_name='property',
            name='description_ru',
            field=models.TextField(null=True, verbose_name='Пълно описание'),
        ),
        migrations.AddField(
            model_name='property',
            name='title_bg',
            field=models.CharField(max_length=200, null=True, verbose_name='Заглавие на обявата'),
        ),
        migrations.AddField(
            model_name='property',
            name='title_el',
            field=models.CharField(max_length=200, null=True, verbose_name='Заглавие на обявата'),
        ),
        migrations.AddField(
            model_name='property',
            name='title_en',
            field=models.CharField(max_length=200, null=True, verbose_name='Заглавие на обявата'),
        ),
        migrations.AddField(
            model_name='property',
            name='title_ru',
            field=models.CharField(max_length=200, null=True, verbose_name='Заглавие на обявата'),
        ),
        migrations.AddField(
            model_name='propertytype',
            name='name_bg',
            field=models.CharField(max_length=50, null=True, verbose_name='Име на типа'),
        ),
        migrations.AddField(
            model_name='propertytype',
            name='name_el',
            field=models.CharField(max_length=50, null=True, verbose_name='Име на типа'),
        ),
        migrations.AddField(
            model_name='propertytype',
            name='name_en',
            field=models.CharField(max_length=50, null=True, verbose_name='Име на типа'),
        ),
        migrations.AddField(
            model_name='propertytype',
            name='name_ru',
            field=models.CharField(max_length=50, null=True, verbose_name='Име на типа'),
        ),
        migrations.AddField(
            model_name='testimonial',
            name='client_name_bg',
            field=models.CharField(max_length=100, null=True, verbose_name='Име на клиента'),
        ),
        migrations.AddField(
            model_name='testimonial',
            name='client_name_el',
            field=models.CharField(max_length=100, null=True, verbose_name='Име на клиента'),
        ),
        migrations.AddField(
            model_name='testimonial',
            name='client_name_en',
            field=models.CharField(max_length=100, null=True, verbose_name='Име на клиента'),
        ),
        migrations.AddField(
            model_name='testimonial',
            name='client_name_ru',
            field=models.CharField(max_length=100, null=True, verbose_name='Име на клиента'),
        ),
        migrations.AddField(
            model_name='testimonial',
            name='quote_bg',
            field=models.TextField(null=True, verbose_name='Текст на отзива'),
        ),
        migrations.AddField(
            model_name='testimonial',
            name='quote_el',
            field=models.TextField(null=True, verbose_name='Текст на отзива'),
        ),
        migrations.AddField(
            model_name='testimonial',
            name='quote_en',
            field=models.TextField(null=True, verbose_name='Текст на отзива'),
        ),
        migrations.AddField(
            model_name='testimonial',
            name='quote_ru',
            field=models.TextField(null=True, verbose_name='Текст на отзива'),
        ),
    ]
