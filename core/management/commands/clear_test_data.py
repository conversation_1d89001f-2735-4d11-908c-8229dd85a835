from django.core.management.base import BaseCommand
from django.utils.translation import gettext_lazy as _
from core.models import Property, PropertyImage, ContactInquiry, Testimonial


class Command(BaseCommand):
    help = _("Изчиства всички тестови данни от базата данни")

    def add_arguments(self, parser):
        parser.add_argument(
            "--confirm",
            action="store_true",
            help=_("Потвърдете, че искате да изчистите всички данни"),
        )

    def handle(self, *args, **options):
        if not options["confirm"]:
            self.stdout.write(
                self.style.WARNING(
                    _(
                        "ВНИМАНИЕ: Тази команда ще изтрие всички имоти, снимки, запитвания и отзиви!\n"
                        "За да потвърдите, изпълнете командата с --confirm флаг:\n"
                        "python manage.py clear_test_data --confirm"
                    )
                )
            )
            return

        self.stdout.write(_("Започвам изчистването на тестовите данни..."))

        # Броене на записите преди изтриване
        properties_count = Property.objects.count()
        images_count = PropertyImage.objects.count()
        inquiries_count = ContactInquiry.objects.count()
        testimonials_count = Testimonial.objects.count()

        # Изтриване на данните
        PropertyImage.objects.all().delete()
        ContactInquiry.objects.all().delete()
        Testimonial.objects.all().delete()
        Property.objects.all().delete()

        self.stdout.write(
            self.style.SUCCESS(
                _(
                    f"Успешно изтрити:\n"
                    f"- {properties_count} имота\n"
                    f"- {images_count} снимки\n"
                    f"- {inquiries_count} запитвания\n"
                    f"- {testimonials_count} отзива"
                )
            )
        )

        self.stdout.write(
            self.style.WARNING(
                _(
                    "Забележка: Градовете, локациите, типовете имоти, особеностите и членовете на екипа "
                    "НЕ са изтрити, за да могат да се използват отново."
                )
            )
        )
