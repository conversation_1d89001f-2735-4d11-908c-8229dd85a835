import random
from decimal import Decimal
from django.core.management.base import BaseCommand
from django.utils.translation import gettext_lazy as _
from core.models import City, Location, PropertyType, Feature, TeamMember, Property


class Command(BaseCommand):
    help = _("Създава тестови данни за имоти в базата данни")

    def add_arguments(self, parser):
        parser.add_argument(
            "--count",
            type=int,
            default=20,
            help=_("Брой имоти за създаване (по подразбиране: 20)"),
        )

    def handle(self, *args, **options):
        count = options["count"]

        self.stdout.write(_("Започвам създаването на тестови данни..."))

        # Създаване на основни данни (ако не съществуват)
        self.create_locations()
        self.create_property_types()
        self.create_features()
        self.create_team_members()

        # Създаване на имоти
        self.create_properties(count)

        self.stdout.write(
            self.style.SUCCESS(_(f"Успешно създадени {count} тестови имота!"))
        )

    def create_locations(self):
        """Създава тестови локации (градове и квартали)"""
        cities_data = [
            {
                "name": "София",
                "name_en": "Sofia",
                "name_el": "Σόφια",
                "name_ru": "София",
            },
            {
                "name": "Пловдив",
                "name_en": "Plovdiv",
                "name_el": "Πλόβντιβ",
                "name_ru": "Пловдив",
            },
            {
                "name": "Варна",
                "name_en": "Varna",
                "name_el": "Βάρνα",
                "name_ru": "Варна",
            },
            {
                "name": "Бургас",
                "name_en": "Burgas",
                "name_el": "Μπουργκάς",
                "name_ru": "Бургас",
            },
            {"name": "Русе", "name_en": "Ruse", "name_el": "Ρούσε", "name_ru": "Русе"},
            {
                "name": "Стара Загора",
                "name_en": "Stara Zagora",
                "name_el": "Στάρα Ζαγόρα",
                "name_ru": "Стара Загора",
            },
        ]

        neighborhoods_data = {
            "София": [
                {
                    "name": "Център",
                    "name_en": "Center",
                    "name_el": "Κέντρο",
                    "name_ru": "Центр",
                },
                {
                    "name": "Лозенец",
                    "name_en": "Lozenets",
                    "name_el": "Λοζένετς",
                    "name_ru": "Лозенец",
                },
                {
                    "name": "Борово",
                    "name_en": "Borovo",
                    "name_el": "Μπόροβο",
                    "name_ru": "Борово",
                },
                {
                    "name": "Младост",
                    "name_en": "Mladost",
                    "name_el": "Μλάντοστ",
                    "name_ru": "Младост",
                },
                {
                    "name": "Люлин",
                    "name_en": "Lyulin",
                    "name_el": "Λιούλιν",
                    "name_ru": "Люлин",
                },
                {
                    "name": "Витоша",
                    "name_en": "Vitosha",
                    "name_el": "Βιτόσα",
                    "name_ru": "Витоша",
                },
                {
                    "name": "Оборище",
                    "name_en": "Oborishte",
                    "name_el": "Ομπόριστε",
                    "name_ru": "Оборище",
                },
                {
                    "name": "Редута",
                    "name_en": "Reduta",
                    "name_el": "Ρεντούτα",
                    "name_ru": "Редута",
                },
            ],
            "Пловдив": [
                {
                    "name": "Център",
                    "name_en": "Center",
                    "name_el": "Κέντρο",
                    "name_ru": "Центр",
                },
                {
                    "name": "Тракия",
                    "name_en": "Thrace",
                    "name_el": "Θράκη",
                    "name_ru": "Тракия",
                },
                {
                    "name": "Каменица",
                    "name_en": "Kamenitsa",
                    "name_el": "Καμενίτσα",
                    "name_ru": "Каменица",
                },
                {
                    "name": "Южен",
                    "name_en": "Southern",
                    "name_el": "Νότιος",
                    "name_ru": "Южный",
                },
                {
                    "name": "Кършияка",
                    "name_en": "Karshiaka",
                    "name_el": "Καρσιάκα",
                    "name_ru": "Кършияка",
                },
                {
                    "name": "Смирненски",
                    "name_en": "Smirnenski",
                    "name_el": "Σμιρνένσκι",
                    "name_ru": "Смирненски",
                },
            ],
            "Варна": [
                {
                    "name": "Център",
                    "name_en": "Center",
                    "name_el": "Κέντρο",
                    "name_ru": "Центр",
                },
                {
                    "name": "Младост",
                    "name_en": "Mladost",
                    "name_el": "Μλάντοστ",
                    "name_ru": "Младост",
                },
                {
                    "name": "Владиславово",
                    "name_en": "Vladislavovo",
                    "name_el": "Βλαντισλάβοβο",
                    "name_ru": "Владиславово",
                },
                {
                    "name": "Възраждане",
                    "name_en": "Vazrazhdane",
                    "name_el": "Βαζραζντάνε",
                    "name_ru": "Возрождение",
                },
                {
                    "name": "Чаталджа",
                    "name_en": "Chataldzha",
                    "name_el": "Τσαταλτζά",
                    "name_ru": "Чаталджа",
                },
            ],
            "Бургас": [
                {
                    "name": "Център",
                    "name_en": "Center",
                    "name_el": "Κέντρο",
                    "name_ru": "Центр",
                },
                {
                    "name": "Лазур",
                    "name_en": "Lazur",
                    "name_el": "Λαζούρ",
                    "name_ru": "Лазур",
                },
                {
                    "name": "Меден рудник",
                    "name_en": "Copper Mine",
                    "name_el": "Χαλκορυχείο",
                    "name_ru": "Медный рудник",
                },
                {
                    "name": "Зорница",
                    "name_en": "Zornitsa",
                    "name_el": "Ζόρνιτσα",
                    "name_ru": "Зорница",
                },
                {
                    "name": "Славейков",
                    "name_en": "Slaveykov",
                    "name_el": "Σλαβέικοβ",
                    "name_ru": "Славейков",
                },
            ],
            "Русе": [
                {
                    "name": "Център",
                    "name_en": "Center",
                    "name_el": "Κέντρο",
                    "name_ru": "Центр",
                },
                {
                    "name": "Чародейка",
                    "name_en": "Charodeyka",
                    "name_el": "Τσαρόντεικα",
                    "name_ru": "Чародейка",
                },
                {
                    "name": "Възраждане",
                    "name_en": "Vazrazhdane",
                    "name_el": "Βαζραζντάνε",
                    "name_ru": "Возрождение",
                },
                {
                    "name": "Дружба",
                    "name_en": "Druzhba",
                    "name_el": "Ντρούζμπα",
                    "name_ru": "Дружба",
                },
            ],
            "Стара Загора": [
                {
                    "name": "Център",
                    "name_en": "Center",
                    "name_el": "Κέντρο",
                    "name_ru": "Центр",
                },
                {
                    "name": "Самара",
                    "name_en": "Samara",
                    "name_el": "Σαμάρα",
                    "name_ru": "Самара",
                },
                {
                    "name": "Железник",
                    "name_en": "Zheleznik",
                    "name_el": "Ζελέζνικ",
                    "name_ru": "Железник",
                },
                {
                    "name": "Три чучура",
                    "name_en": "Tri Chuchura",
                    "name_el": "Τρι Τσουτσούρα",
                    "name_ru": "Три чучура",
                },
            ],
        }

        for city_data in cities_data:
            city, created = City.objects.get_or_create(
                name=city_data["name"],
                defaults={
                    "name_en": city_data["name_en"],
                    "name_el": city_data["name_el"],
                    "name_ru": city_data["name_ru"],
                },
            )
            if created:
                self.stdout.write(_(f"Създаден град: {city.name}"))

            # Създаване на квартали за този град
            city_name = city_data["name"]
            if city_name in neighborhoods_data:
                for neighborhood_data in neighborhoods_data[city_name]:
                    neighborhood, created = Location.objects.get_or_create(
                        name=neighborhood_data["name"],
                        city=city,
                        defaults={
                            "name_en": neighborhood_data["name_en"],
                            "name_el": neighborhood_data["name_el"],
                            "name_ru": neighborhood_data["name_ru"],
                        },
                    )
                    if created:
                        self.stdout.write(
                            _(f"Създаден квартал: {neighborhood.name}, {city.name}")
                        )

    def create_property_types(self):
        """Създава типове имоти"""
        types_data = [
            ("Апартамент", "apartment", "Apartment", "Διαμέρισμα", "Квартира"),
            ("Къща", "house", "House", "Σπίτι", "Дом"),
            ("Вила", "villa", "Villa", "Βίλα", "Вилла"),
            ("Студио", "studio", "Studio", "Στούντιο", "Студия"),
            ("Мезонет", "maisonette", "Maisonette", "Μεζονέτα", "Мезонет"),
            ("Офис", "office", "Office", "Γραφείο", "Офис"),
            ("Магазин", "shop", "Shop", "Κατάστημα", "Магазин"),
            ("Гараж", "garage", "Garage", "Γκαράζ", "Гараж"),
            ("Парцел", "plot", "Plot", "Οικόπεδο", "Участок"),
        ]

        for type_name, slug, name_en, name_el, name_ru in types_data:
            property_type, created = PropertyType.objects.get_or_create(
                name=type_name,
                defaults={
                    "name_en": name_en,
                    "name_el": name_el,
                    "name_ru": name_ru,
                },
            )
            if created:
                self.stdout.write(_(f"Създаден тип имот: {property_type.name}"))

    def create_features(self):
        """Създава особености на имотите"""
        features_data = [
            ("Асансьор", "Elevator", "Ανελκυστήρας", "Лифт"),
            ("Гараж", "Garage", "Γκαράζ", "Гараж"),
            ("Балкон", "Balcony", "Μπαλκόνι", "Балкон"),
            ("Тераса", "Terrace", "Τεράτσα", "Терраса"),
            ("Градина", "Garden", "Κήπος", "Сад"),
            ("Климатик", "Air Conditioning", "Κλιματισμός", "Кондиционер"),
            ("Паркомясто", "Parking", "Χώρος στάθμευσης", "Парковка"),
            ("Мазе", "Basement", "Υπόγειο", "Подвал"),
            ("Таван", "Attic", "Σοφίτα", "Чердак"),
            ("Нова сграда", "New Building", "Νέο κτίριο", "Новое здание"),
            ("Тухла", "Brick", "Τούβλο", "Кирпич"),
            ("Панел", "Panel", "Πάνελ", "Панель"),
            ("ЕПК", "EPC", "ΕΠΚ", "ЕПК"),
            ("Обзаведен", "Furnished", "Επιπλωμένο", "Меблированный"),
            ("Необзаведен", "Unfurnished", "Ανεπιπλωμένο", "Немеблированный"),
            ("Лукс", "Luxury", "Πολυτέλεια", "Люкс"),
            ("Саниран", "Renovated", "Ανακαινισμένο", "Отремонтированный"),
            ("Южно изложение", "South Exposure", "Νότια έκθεση", "Южная экспозиция"),
            ("Изток-Запад", "East-West", "Ανατολή-Δύση", "Восток-Запад"),
            ("Север-Юг", "North-South", "Βορράς-Νότος", "Север-Юг"),
        ]

        for feature_name, name_en, name_el, name_ru in features_data:
            feature, created = Feature.objects.get_or_create(
                name=feature_name,
                defaults={
                    "name_en": name_en,
                    "name_el": name_el,
                    "name_ru": name_ru,
                },
            )
            if created:
                self.stdout.write(_(f"Създадена особеност: {feature.name}"))

    def create_team_members(self):
        """Създава членове на екипа (брокери)"""
        team_data = [
            {
                "name": "Иван Петров",
                "name_en": "Ivan Petrov",
                "name_el": "Ιβάν Πέτροβ",
                "name_ru": "Иван Петров",
                "phone": "+359 888 123 456",
                "email": "<EMAIL>",
                "title": "Старши брокер",
                "title_en": "Senior Broker",
                "title_el": "Ανώτερος Μπρόκερ",
                "title_ru": "Старший брокер",
            },
            {
                "name": "Мария Георгиева",
                "name_en": "Maria Georgieva",
                "name_el": "Μαρία Γεωργίεβα",
                "name_ru": "Мария Георгиева",
                "phone": "+359 888 234 567",
                "email": "<EMAIL>",
                "title": "Брокер",
                "title_en": "Broker",
                "title_el": "Μπρόκερ",
                "title_ru": "Брокер",
            },
            {
                "name": "Петър Иванов",
                "name_en": "Peter Ivanov",
                "name_el": "Πέτερ Ιβάνοβ",
                "name_ru": "Петр Иванов",
                "phone": "+359 888 345 678",
                "email": "<EMAIL>",
                "title": "Младши брокер",
                "title_en": "Junior Broker",
                "title_el": "Τζούνιορ Μπρόκερ",
                "title_ru": "Младший брокер",
            },
            {
                "name": "Елена Стоянова",
                "name_en": "Elena Stoyanova",
                "name_el": "Ελένα Στογιάνοβα",
                "name_ru": "Елена Стоянова",
                "phone": "+359 888 456 789",
                "email": "<EMAIL>",
                "title": "Брокер",
                "title_en": "Broker",
                "title_el": "Μπρόκερ",
                "title_ru": "Брокер",
            },
            {
                "name": "Димитър Николов",
                "name_en": "Dimitar Nikolov",
                "name_el": "Ντιμίταρ Νικολόβ",
                "name_ru": "Димитр Николов",
                "phone": "+359 888 567 890",
                "email": "<EMAIL>",
                "title": "Мениджър продажби",
                "title_en": "Sales Manager",
                "title_el": "Διευθυντής Πωλήσεων",
                "title_ru": "Менеджер по продажам",
            },
        ]

        for member_data in team_data:
            member, created = TeamMember.objects.get_or_create(
                email=member_data["email"], defaults=member_data
            )
            if created:
                self.stdout.write(_(f"Създаден брокер: {member.name}"))

    def create_properties(self, count):
        """Създава тестови имоти"""

        # Получаване на всички налични данни
        locations = list(Location.objects.all())  # Всички локации вече са квартали
        property_types = list(PropertyType.objects.all())
        features = list(Feature.objects.all())
        team_members = list(TeamMember.objects.all())

        if not all([locations, property_types, team_members]):
            self.stdout.write(
                self.style.ERROR(
                    _(
                        "Няма достатъчно данни за създаване на имоти. Моля, проверете дали са създадени локации, типове и брокери."
                    )
                )
            )
            return

        # Примерни заглавия и описания за всеки език
        property_titles_templates = {
            "bg": [
                "{bedrooms}-стаен апартамент в {location}",
                "Просторен {property_type} в {location}",
                "Луксозен {property_type} - {location}",
                "Нов {property_type} в центъра на {location}",
                "Обзаведен {property_type} - {location}",
                "Светъл {property_type} с панорамна гледка - {location}",
                "Реновиран {property_type} в {location}",
                "{property_type} близо до метро - {location}",
            ],
            "en": [
                "{bedrooms}-room apartment in {location}",
                "Spacious {property_type} in {location}",
                "Luxury {property_type} - {location}",
                "New {property_type} in the center of {location}",
                "Furnished {property_type} - {location}",
                "Bright {property_type} with panoramic view - {location}",
                "Renovated {property_type} in {location}",
                "{property_type} near metro - {location}",
            ],
            "el": [
                "{bedrooms}-δωμάτιο διαμέρισμα στην {location}",
                "Ευρύχωρο {property_type} στην {location}",
                "Πολυτελές {property_type} - {location}",
                "Νέο {property_type} στο κέντρο της {location}",
                "Επιπλωμένο {property_type} - {location}",
                "Φωτεινό {property_type} με πανοραμική θέα - {location}",
                "Ανακαινισμένο {property_type} στην {location}",
                "{property_type} κοντά στο μετρό - {location}",
            ],
            "ru": [
                "{bedrooms}-комнатная квартира в {location}",
                "Просторный {property_type} в {location}",
                "Роскошный {property_type} - {location}",
                "Новый {property_type} в центре {location}",
                "Меблированный {property_type} - {location}",
                "Светлый {property_type} с панорамным видом - {location}",
                "Отремонтированный {property_type} в {location}",
                "{property_type} рядом с метро - {location}",
            ],
        }

        descriptions_templates = {
            "bg": [
                "Предлагаме ви този прекрасен имот, разположен в престижния район {location}. "
                "Имотът разполага с площ от {area} кв.м. и предлага отлични възможности за живеене. "
                "Намира се в близост до училища, магазини и обществен транспорт.",
                "Уникална възможност за придобиване на имот в сърцето на {location}. "
                "С площ от {area} кв.м., този {property_type} предлага комфорт и удобство. "
                "Идеален за семейства или инвеститори.",
                "Модерен {property_type} с площ {area} кв.м. в желания район {location}. "
                "Имотът е в отлично състояние и е готов за нанасяне. "
                "Разполага с всички необходими удобства за комфортен живот.",
                "Престижен имот в {location} с площ от {area} кв.м. "
                "Отличен избор за тези, които търсят качество и комфорт. "
                "Лесен достъп до центъра на града и основните транспортни артерии.",
            ],
            "en": [
                "We offer you this wonderful property located in the prestigious {location} district. "
                "The property has an area of {area} sq.m. and offers excellent living opportunities. "
                "It is located near schools, shops and public transport.",
                "Unique opportunity to acquire a property in the heart of {location}. "
                "With an area of {area} sq.m., this {property_type} offers comfort and convenience. "
                "Perfect for families or investors.",
                "Modern {property_type} with an area of {area} sq.m. in the desired {location} district. "
                "The property is in excellent condition and ready for occupancy. "
                "It has all the necessary amenities for comfortable living.",
                "Prestigious property in {location} with an area of {area} sq.m. "
                "Excellent choice for those looking for quality and comfort. "
                "Easy access to the city center and main transport arteries.",
            ],
            "el": [
                "Σας προσφέρουμε αυτή την υπέροχη ιδιοκτησία που βρίσκεται στην αξιόλογη περιοχή {location}. "
                "Η ιδιοκτησία έχει έκταση {area} τ.μ. και προσφέρει εξαιρετικές ευκαιρίες διαβίωσης. "
                "Βρίσκεται κοντά σε σχολεία, καταστήματα και δημόσια συγκοινωνία.",
                "Μοναδική ευκαιρία για την απόκτηση ιδιοκτησίας στην καρδιά της {location}. "
                "Με έκταση {area} τ.μ., αυτό το {property_type} προσφέρει άνεση και ευκολία. "
                "Ιδανικό για οικογένειες ή επενδυτές.",
                "Μοντέρνο {property_type} με έκταση {area} τ.μ. στην επιθυμητή περιοχή {location}. "
                "Η ιδιοκτησία είναι σε άριστη κατάσταση και έτοιμη για κατοίκηση. "
                "Διαθέτει όλες τις απαραίτητες ανέσεις για άνετη διαβίωση.",
                "Αξιόλογη ιδιοκτησία στην {location} με έκταση {area} τ.μ. "
                "Εξαιρετική επιλογή για όσους αναζητούν ποιότητα και άνεση. "
                "Εύκολη πρόσβαση στο κέντρο της πόλης και στους κύριους αρτηριακούς δρόμους.",
            ],
            "ru": [
                "Предлагаем вам эту прекрасную недвижимость, расположенную в престижном районе {location}. "
                "Недвижимость имеет площадь {area} кв.м. и предлагает отличные возможности для проживания. "
                "Находится рядом со школами, магазинами и общественным транспортом.",
                "Уникальная возможность приобрести недвижимость в сердце {location}. "
                "С площадью {area} кв.м., этот {property_type} предлагает комфорт и удобство. "
                "Идеально подходит для семей или инвесторов.",
                "Современный {property_type} площадью {area} кв.м. в желаемом районе {location}. "
                "Недвижимость в отличном состоянии и готова к заселению. "
                "Имеет все необходимые удобства для комфортной жизни.",
                "Престижная недвижимость в {location} площадью {area} кв.м. "
                "Отличный выбор для тех, кто ищет качество и комфорт. "
                "Легкий доступ к центру города и основным транспортным артериям.",
            ],
        }

        created_count = 0

        for i in range(count):
            try:
                # Случайни параметри
                location = random.choice(locations)
                property_type = random.choice(property_types)
                bedrooms = random.randint(1, 4)
                area = random.randint(40, 250)

                # Генериране на цена спрямо площта и локацията
                base_price_per_sqm = random.randint(800, 2500)
                if "София" in str(location.city.name):
                    base_price_per_sqm *= 1.3
                elif "Пловдив" in str(location.city.name) or "Варна" in str(
                    location.city.name
                ):
                    base_price_per_sqm *= 1.1

                price = Decimal(str(area * base_price_per_sqm))

                # Генериране на заглавия за всички езици
                titles = {}
                for lang in ["bg", "en", "el", "ru"]:
                    title_template = random.choice(property_titles_templates[lang])
                    titles[f"title_{lang}"] = title_template.format(
                        bedrooms=bedrooms,
                        property_type=property_type.name.lower(),
                        location=location.name,
                    )

                # Генериране на описания за всички езици
                descriptions = {}
                for lang in ["bg", "en", "el", "ru"]:
                    description_template = random.choice(descriptions_templates[lang])
                    descriptions[f"description_{lang}"] = description_template.format(
                        location=location.name,
                        area=area,
                        property_type=property_type.name.lower(),
                    )

                # Създаване на имота
                property_data = {
                    "property_type": property_type,
                    "location": location,
                    "price": price,
                    "area": area,
                    "bedrooms": bedrooms,
                    "assigned_broker": random.choice(team_members),
                    "is_published": random.choice(
                        [True, True, True, False]
                    ),  # 75% публикувани
                    "is_featured": random.choice(
                        [True, False, False, False]
                    ),  # 25% препоръчани
                }
                property_data.update(titles)
                property_data.update(descriptions)

                property_obj = Property.objects.create(**property_data)

                # Добавяне на случайни особености
                selected_features = random.sample(
                    features, random.randint(2, min(6, len(features)))
                )
                property_obj.features.set(selected_features)

                created_count += 1
                self.stdout.write(
                    _(f"Създаден имот {created_count}/{count}: {property_obj.title}")
                )

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        _(f"Грешка при създаването на имот {i+1}: {str(e)}")
                    )
                )

        self.stdout.write(_(f"Общо създадени имоти: {created_count}"))
