from django.db import models
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from autoslug import AutoSlugField


class City(models.Model):
    """Модел за градове"""

    name = models.CharField(max_length=100, verbose_name=_("Име на града"))

    class Meta:
        verbose_name = _("Град")
        verbose_name_plural = _("Градове")
        ordering = ["name"]

    def __str__(self):
        return self.name


class Location(models.Model):
    """Модел за локации (градове, квартали) с йерархична структура"""

    name = models.CharField(max_length=100, verbose_name=_("Име на локацията"))
    city = models.ForeignKey(
        City, on_delete=models.CASCADE, null=True, blank=True, verbose_name=_("Град")
    )

    class Meta:
        verbose_name = _("Локация")
        verbose_name_plural = _("Локации")
        ordering = ["city", "name"]

    def __str__(self):
        if self.city:
            return f"{self.name}, {self.city.name}"
        return self.name


class PropertyType(models.Model):
    """Модел за типовете имоти (Апартамент, Къща, и др.)"""

    name = models.CharField(max_length=50, verbose_name=_("Име на типа"))
    slug = AutoSlugField(
        populate_from="name",
        unique=True,
        always_update=False,
        max_length=50,
        verbose_name=_("URL адрес"),
    )

    class Meta:
        verbose_name = _("Тип имот")
        verbose_name_plural = _("Типове имоти")
        ordering = ["name"]

    def __str__(self):
        return self.name


class Feature(models.Model):
    """Модел за особеностите на имотите (Гараж, Асансьор, и др.)"""

    name = models.CharField(max_length=50, verbose_name=_("Име на особеността"))

    class Meta:
        verbose_name = _("Особеност")
        verbose_name_plural = _("Особености")
        ordering = ["name"]

    def __str__(self):
        return self.name


class TeamMember(models.Model):
    """Модел за членовете на екипа (брокери)"""

    name = models.CharField(max_length=100, verbose_name=_("Име"))
    photo = models.ImageField(
        upload_to="team_photos/", verbose_name=_("Снимка"), blank=True, null=True
    )
    phone = models.CharField(max_length=20, verbose_name=_("Телефонен номер"))
    email = models.EmailField(verbose_name=_("Имейл адрес"))
    title = models.CharField(max_length=100, verbose_name=_("Длъжност"))

    class Meta:
        verbose_name = _("Член на екипа")
        verbose_name_plural = _("Членове на екипа")
        ordering = ["name"]

    def __str__(self):
        return f"{self.name} - {self.title}"


class Property(models.Model):
    """Основният модел за обявите за имоти"""

    title = models.CharField(max_length=200, verbose_name=_("Заглавие на обявата"))
    slug = AutoSlugField(
        populate_from="title",
        unique=True,
        always_update=False,
        max_length=200,
        verbose_name=_("URL адрес"),
    )
    property_type = models.ForeignKey(
        PropertyType, on_delete=models.CASCADE, verbose_name=_("Тип имот")
    )
    location = models.ForeignKey(
        Location, on_delete=models.CASCADE, verbose_name=_("Локация")
    )
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name=_("Цена"))
    area = models.IntegerField(verbose_name=_("Площ (кв.м.)"))
    bedrooms = models.PositiveIntegerField(verbose_name=_("Брой спални"), default=0)
    description = models.TextField(verbose_name=_("Пълно описание"))
    features = models.ManyToManyField(Feature, blank=True, verbose_name=_("Особености"))
    assigned_broker = models.ForeignKey(
        TeamMember, on_delete=models.CASCADE, verbose_name=_("Отговорен брокер")
    )
    is_published = models.BooleanField(
        default=True, verbose_name=_("Публикувана обява")
    )
    is_featured = models.BooleanField(default=False, verbose_name=_("Препоръчан имот"))
    created_at = models.DateTimeField(
        auto_now_add=True, verbose_name=_("Дата на създаване")
    )
    updated_at = models.DateTimeField(
        auto_now=True, verbose_name=_("Дата на обновяване")
    )

    class Meta:
        verbose_name = _("Имот")
        verbose_name_plural = _("Имоти")
        ordering = ["-created_at"]

    def __str__(self):
        return self.title

    def get_absolute_url(self):
        return reverse("core:property-detail", kwargs={"slug": self.slug})

    @property
    def main_image(self):
        """Връща главната снимка на имота (първата в поредността)"""
        return self.images.filter(order=1).first()

    @property
    def price_formatted(self):
        """Форматирана цена с валута"""
        return f"{self.price:,.0f} лв."


class PropertyImage(models.Model):
    """Модел за галерията със снимки към всеки имот"""

    property = models.ForeignKey(
        Property,
        on_delete=models.CASCADE,
        related_name="images",
        verbose_name=_("Имот"),
    )
    image = models.ImageField(upload_to="property_images/", verbose_name=_("Снимка"))
    order = models.PositiveIntegerField(default=1, verbose_name=_("Поредност"))

    class Meta:
        verbose_name = _("Снимка на имот")
        verbose_name_plural = _("Снимки на имоти")
        ordering = ["order"]
        unique_together = ["property", "order"]

    def __str__(self):
        return f"Снимка {self.order} - {self.property.title}"


class Testimonial(models.Model):
    """Модел за отзиви от клиенти"""

    client_name = models.CharField(max_length=100, verbose_name=_("Име на клиента"))
    quote = models.TextField(verbose_name=_("Текст на отзива"))
    is_active = models.BooleanField(default=True, verbose_name=_("Активен отзив"))

    class Meta:
        verbose_name = _("Отзив")
        verbose_name_plural = _("Отзиви")
        ordering = ["client_name"]

    def __str__(self):
        return f"Отзив от {self.client_name}"


class ContactInquiry(models.Model):
    """Модел за съхранение на запитвания от формите за контакт"""

    name = models.CharField(max_length=100, verbose_name=_("Име"))
    email = models.EmailField(verbose_name=_("Имейл адрес"))
    phone = models.CharField(max_length=20, verbose_name=_("Телефонен номер"))
    message = models.TextField(verbose_name=_("Съобщение"))
    property = models.ForeignKey(
        Property,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_("Имот (ако е за конкретна обява)"),
    )
    created_at = models.DateTimeField(
        auto_now_add=True, verbose_name=_("Дата на получаване")
    )
    is_handled = models.BooleanField(
        default=False, verbose_name=_("Обработено запитване")
    )

    class Meta:
        verbose_name = _("Запитване за контакт")
        verbose_name_plural = _("Запитвания за контакт")
        ordering = ["-created_at"]

    def __str__(self):
        if self.property:
            return f"Запитване от {self.name} за {self.property.title}"
        return f"Общо запитване от {self.name}"
