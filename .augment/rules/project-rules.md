---
type: "always_apply"
---

### Технологичен стак (Tech Stack)

Проектът използва следните основни технологии:

-   **Backend**: Django v5
-   **Frontend**: Tailwind CSS v4, Alpine.js, htmx
-   **Build Tool**: Vite.js

### Структура на проекта

Разбирането на структурата е ключово за работата по проекта. Ето основните директории и тяхното предназначение:

-   `config/`: Основната конфигурация на Django проекта. Тук се намират `settings.py`, `urls.py` (главният) и `wsgi.py`/`asgi.py`.
-   `core/`: Главното Django приложение.
    -   `models.py`: Моделите на данните.
    -   `views.py`: Изгледите, които обработват заявките.
    -   `urls.py`: URL пътищата за това приложение.
    -   `templates/`: HTML темплейтите.
-   `static/`: Глобални статични файлове, които се обработват от Vite.
    -   `css/`: Главният CSS файл (`main.css`) с дефинициите на темите за Tailwind.
    -   `js/`: Главният JavaScript файл (`main.js`), където се инициализира Alpine.js и се импортират други скриптове.
-   `assets/`: Компилирани и готови за продукция файлове (CSS, JS), генерирани от Vite. **Тази папка не се редактира ръчно.**
-   `requirements.txt`: Списък с Python зависимости (за `pip`).
-   `package.json`: Списък с JavaScript зависимости (за `npm`).
-   `vite.config.mjs`: Конфигурационен файл за Vite.

---

### 1. Tailwind CSS - Стилизация

Най-важното правило е да се придържаме към дефинираните в `static/css/main.css` теми.
**Никога не трябва да се използват хардкоднати стойности за цветове, шрифтове или размери, когато има дефинирана семантична променлива.**

#### 1.1. Цветове (Colors)

Винаги използвайте семантичните имена на цветовете от темата. Файлът `main.css` дефинира променливи за светла и тъмна тема.

-   **Правилно**: `bg-primary`, `text-on-surface`, `border-outline`, `dark:bg-primary-dark`
-   **НЕПРАВИЛНО**: `bg-blue-600`, `text-gray-800`, `border-zinc-300`

Пример:

```html
<!-- Правилно -->
<button class="bg-primary text-on-primary hover:bg-secondary">
    Натисни ме
</button>

<!-- Неправилно -->
<button class="bg-sky-700 text-white hover:bg-black">Натисни ме</button>
```

#### 1.2. Шрифтове (Fonts)

Използвайте променливите за шрифтове, дефинирани в темата.

-   **Правилно**: `font-title`, `font-body`
-   **НЕПРАВИЛНО**: `font-['Merriweather']`, `font-sans` (освен ако не е базовият шрифт)

#### 1.3. Радиус на рамки (Border Radius)

Използвайте променливата за радиус от темата, за да има консистентност в целия дизайн.

-   **Правилно**: `rounded-radius`
-   **НЕПРАВИЛНО**: `rounded-lg`, `rounded-md`, `rounded-xl`

### 2. JavaScript - Alpine.js

#### 2.1. Дефиниране на компоненти

-   За много проста, еднократна логика, можете да дефинирате `x-data` директно в HTML темплейта.
-   За по-сложна или преизползваема логика, изнесете я в `static/js/` файлове и използвайте `Alpine.data()` за регистриране на компоненти. Това поддържа темплейтите чисти.

Пример за преизползваем компонент:

```javascript
// static/js/components/dropdown.js
document.addEventListener('alpine:init', () => {
    Alpine.data('dropdown', () => ({
        open: false,
        toggle() {
            this.open = !this.open
        },
    }))
})

// template.html
<div x-data="dropdown">...</div>
```

#### 2.2. Избягвайте сложна логика в `x-init`

Използвайте `x-init` за прости инициализации. По-сложната логика трябва да е в методите на компонента.

### 3. Django Templates & htmx

#### 3.1. Структура

-   Използвайте `{% extends 'core/base.html' %}`.
-   Разделяйте големи темплейти на по-малки, преизползваеми части с `{% include 'path/to/partial.html' %}`.
-   За htmx заявки, които връщат части от страницата, създавайте отделни "partial" темплейти. Те не трябва да разширяват `base.html`.

#### 3.2. htmx атрибути

-   Групирайте `hx-*` атрибутите заедно за по-добра четимост.
-   Използвайте `hx-indicator` за обратна връзка с потребителя по време на заявка.

Пример:

```html
<button
    hx-post="{% url 'my_action' %}"
    hx-target="#content"
    hx-swap="outerHTML"
    hx-indicator="#loading-spinner"
>
    Изпълни действие
</button>
<img
    id="loading-spinner"
    class="htmx-indicator"
    src="{% static 'img/loader.gif' %}"
/>
```

### 4. Django - Backend

#### 4.1. "Fat Models, Thin Views"

-   **Модели (`models.py`)**: Тук трябва да се намира основната бизнес логика. Използвайте properties и методи на модела, за да капсулирате логика, свързана с данните.
-   **Изгледи (`views.py`)**: Трябва да са възможно най-опростени. Тяхната роля е да получат заявката, да извикат необходимата бизнес логика от моделите или услуги (services) и да върнат HTTP отговор.
-   **Услуги (Services)**: За бизнес логика, която не принадлежи на един конкретен модел, може да се създаде отделен `services.py` файл в съответното приложение.

#### 4.2. URL Paths

-   Винаги именувайте URL пътищата с `name="..."`.
-   Използвайте консистентна схема за именуване (напр. `app_name:model_name-action`).

### 5. Общи правила

-   **Коментари**: Пишете коментари на **български език**, за да се запази езиковата консистентност на проекта.
-   **Форматиране**: Използвайте `djlint` за форматиране на Django темплейти и `black` (ако е инсталиран) за Python код.

### 5. Общи правила

-   **Коментари**: Пишете коментари на **български език**, за да се запази езиковата консистентност на проекта.
-   **Форматиране**: Използвайте `djlint` за форматиране на Django темплейти и `black` (ако е инсталиран) за Python код.
