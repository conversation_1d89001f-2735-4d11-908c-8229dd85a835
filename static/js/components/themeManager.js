document.addEventListener("alpine:init", () => {
  Alpine.data("themeManager", () => ({
    // Default to 'light' theme if no setting is found in localStorage
    theme: localStorage.getItem("theme") || "light",

    init() {
      // Set the theme on initial load
      this.setTheme(this.theme);

      // Listen for changes in localStorage from other tabs
      window.addEventListener("storage", (event) => {
        if (event.key === "theme") {
          this.setTheme(event.newValue);
        }
      });
    },

    get darkMode() {
      return this.theme === "dark";
    },

    setTheme(newTheme) {
      this.theme = newTheme;
      localStorage.setItem("theme", newTheme);
      document.documentElement.setAttribute("data-theme", newTheme);
    },

    toggleTheme() {
      const newTheme = this.theme === "light" ? "dark" : "light";
      this.setTheme(newTheme);
    },
  }));

  // Компонент за мобилното меню
  Alpine.data("mobileMenu", () => ({
    open: false,

    toggle() {
      this.open = !this.open;
    },

    close() {
      this.open = false;
    },
  }));
});
