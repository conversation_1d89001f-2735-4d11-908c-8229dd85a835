/**
 * Language Switcher Component
 * Handles language switching functionality with dropdown interface
 */
document.addEventListener("alpine:init", () => {
  Alpine.data("languageSwitcher", () => ({
    open: false,
    currentLanguage: document.documentElement.lang || "bg",

    languages: [
      { code: "bg", name: "Български", flag: "🇧🇬" },
      { code: "en", name: "English", flag: "🇺🇸" },
      { code: "el", name: "Ελληνικά", flag: "🇬🇷" },
      { code: "ru", name: "Русский", flag: "🇷🇺" },
    ],

    init() {
      // Close dropdown when clicking outside
      document.addEventListener("click", (e) => {
        if (!this.$root.contains(e.target)) {
          this.open = false;
        }
      });
    },

    toggle() {
      this.open = !this.open;
    },

    switchLanguage(languageCode) {
      // Get current URL path
      const currentPath = window.location.pathname;

      // Remove current language prefix if it exists
      const pathWithoutLang = currentPath.replace(/^\/[a-z]{2}\//, "/");

      // Create new URL with selected language prefix
      const newPath = `/${languageCode}${
        pathWithoutLang === "/" ? "" : pathWithoutLang
      }`;

      // Navigate to new language version
      window.location.href = newPath;
    },

    getCurrentLanguageName() {
      const lang = this.languages.find((l) => l.code === this.currentLanguage);
      return lang ? lang.name : "Български";
    },

    getCurrentLanguageFlag() {
      const lang = this.languages.find((l) => l.code === this.currentLanguage);
      return lang ? lang.flag : "🇧🇬";
    },
  }));
});
